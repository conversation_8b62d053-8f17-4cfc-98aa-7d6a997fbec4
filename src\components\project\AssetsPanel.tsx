import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Image, Type, Shapes, Palette } from 'lucide-react';

interface AssetsPanelProps {
  projectId: string;
  onElementAdd: (elementId: string) => void;
}

const AssetsPanel = ({ projectId, onElementAdd }: AssetsPanelProps) => {
  const [activeTab, setActiveTab] = useState('templates');

  const handleAddText = () => {
    const elementId = `text-${Date.now()}`;
    onElementAdd(elementId);
  };

  const handleAddShape = (shape: string) => {
    const elementId = `${shape}-${Date.now()}`;
    onElementAdd(elementId);
  };

  const handleUploadImage = () => {
    // TODO: Implement image upload
    console.log('Upload image');
  };

  return (
    <Card className="h-full bg-card border-border rounded-lg shadow-sm">
      <div className="p-4 h-full flex flex-col">
        <h2 className="text-lg font-semibold text-card-foreground mb-4">Assets</h2>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="templates" className="text-xs">
              <Image className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="text" className="text-xs">
              <Type className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="shapes" className="text-xs">
              <Shapes className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="colors" className="text-xs">
              <Palette className="h-3 w-3" />
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 mt-4 overflow-y-auto">
            <TabsContent value="templates" className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                onClick={handleUploadImage}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
              
              <div className="grid grid-cols-2 gap-2">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div 
                    key={i}
                    className="aspect-square bg-muted rounded cursor-pointer hover:bg-muted/80 transition-colors flex items-center justify-center"
                  >
                    <span className="text-xs text-muted-foreground">Template {i}</span>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="text" className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                onClick={handleAddText}
              >
                <Type className="h-4 w-4 mr-2" />
                Add Text
              </Button>
              
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Text Styles</div>
                {['Heading', 'Subheading', 'Body', 'Caption'].map((style) => (
                  <Button
                    key={style}
                    variant="ghost"
                    className="w-full justify-start h-auto p-2"
                    onClick={handleAddText}
                  >
                    <span className={`${style === 'Heading' ? 'text-xl font-bold' : 
                                      style === 'Subheading' ? 'text-lg font-semibold' :
                                      style === 'Body' ? 'text-base' : 'text-sm'}`}>
                      {style}
                    </span>
                  </Button>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="shapes" className="space-y-3">
              <div className="grid grid-cols-3 gap-2">
                {[
                  { name: 'Rectangle', element: '⬜' },
                  { name: 'Circle', element: '⭕' },
                  { name: 'Triangle', element: '🔺' },
                  { name: 'Star', element: '⭐' },
                  { name: 'Arrow', element: '➡️' },
                  { name: 'Line', element: '➖' }
                ].map((shape) => (
                  <Button
                    key={shape.name}
                    variant="outline"
                    className="aspect-square p-2"
                    onClick={() => handleAddShape(shape.name.toLowerCase())}
                  >
                    <span className="text-xl">{shape.element}</span>
                  </Button>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="colors" className="space-y-3">
              <div className="text-sm text-muted-foreground mb-2">Brand Colors</div>
              <div className="grid grid-cols-4 gap-2">
                {[
                  'hsl(var(--primary))',
                  'hsl(var(--secondary))',
                  'hsl(var(--accent))',
                  'hsl(var(--muted))',
                  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
                  '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
                ].map((color, i) => (
                  <div
                    key={i}
                    className="aspect-square rounded cursor-pointer border border-border hover:scale-105 transition-transform"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </Card>
  );
};

export default AssetsPanel;