import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, AlertCircle, Clock, X } from 'lucide-react';
import { useProjectJobs } from '@/hooks/useProjectJobs';
import { cn } from '@/lib/utils';

interface ProjectJobsStatusProps {
  projectId: string | null;
  className?: string;
}

export const ProjectJobsStatus: React.FC<ProjectJobsStatusProps> = ({ 
  projectId, 
  className 
}) => {
  const [dismissedJobs, setDismissedJobs] = useState<string[]>([]);
  const { jobs, activeJobs, completedJobs, errorJobs, hasActiveJobs, hasErrors, isLoading } = useProjectJobs(projectId);

  if (!projectId || isLoading) return null;

  // Filter out dismissed jobs
  const visibleJobs = jobs.filter(job => !dismissedJobs.includes(job.id));
  const visibleActiveJobs = activeJobs.filter(job => !dismissedJobs.includes(job.id));
  const visibleCompletedJobs = completedJobs.filter(job => !dismissedJobs.includes(job.id));
  const visibleErrorJobs = errorJobs.filter(job => !dismissedJobs.includes(job.id));

  // Don't show if no visible jobs
  if (visibleJobs.length === 0) return null;

  const handleDismissJob = (jobId: string) => {
    setDismissedJobs(prev => [...prev, jobId]);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-3 w-3 animate-spin" />;
      case 'queued':
        return <Clock className="h-3 w-3" />;
      case 'done':
        return <CheckCircle className="h-3 w-3" />;
      case 'error':
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-primary text-primary-foreground';
      case 'queued':
        return 'bg-muted text-muted-foreground';
      case 'done':
        return 'bg-accent text-accent-foreground';
      case 'error':
        return 'bg-destructive text-destructive-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const totalActive = visibleActiveJobs.length;
  const hasVisibleErrors = visibleErrorJobs.length > 0;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-6 px-2 text-xs transition-smooth",
            hasVisibleErrors 
              ? "text-destructive hover:text-destructive" 
              : totalActive > 0 
                ? "text-primary hover:text-primary" 
                : "text-muted-foreground hover:text-muted-foreground",
            className
          )}
        >
          {totalActive > 0 && (
            <>
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              {totalActive} running
            </>
          )}
          {totalActive === 0 && hasVisibleErrors && (
            <>
              <AlertCircle className="h-3 w-3 mr-1" />
              {visibleErrorJobs.length} error{visibleErrorJobs.length !== 1 ? 's' : ''}
            </>
          )}
          {totalActive === 0 && !hasVisibleErrors && visibleCompletedJobs.length > 0 && (
            <>
              <CheckCircle className="h-3 w-3 mr-1" />
              {visibleCompletedJobs.length} complete
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-3" align="start" side="bottom">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Project Jobs</h4>
            <Badge variant="secondary" className="text-xs">
              {visibleJobs.length} total
            </Badge>
          </div>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {visibleJobs.map((job) => (
              <div
                key={job.id}
                className="border rounded-lg p-3 space-y-2 bg-card"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(job.status)}
                    <Badge 
                      className={cn("text-xs capitalize", getStatusColor(job.status))}
                    >
                      {job.status}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => handleDismissJob(job.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>

                {job.message && (
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {job.message}
                  </p>
                )}

                {job.status === 'running' && job.progress > 0 && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span>Progress</span>
                      <span>{job.progress}%</span>
                    </div>
                    <Progress value={job.progress} className="h-1" />
                  </div>
                )}

                {job.error && job.status === 'error' && (
                  <p className="text-xs text-destructive line-clamp-2">
                    Error: {job.error}
                  </p>
                )}

                <div className="text-xs text-muted-foreground">
                  {new Date(job.created_at).toLocaleString()}
                </div>
              </div>
            ))}

            {visibleJobs.length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-4">
                No jobs found for this project
              </p>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};