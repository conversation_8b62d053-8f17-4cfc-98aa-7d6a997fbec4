import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef } from 'react';
import { Client } from '@langchain/langgraph-sdk';

const LANGGRAPH_URL = "https://lancatchup.onrender.com";
const LANGGRAPH_ASSISTANT_ID = "catchup";

interface MonitoringMetrics {
  activeThreads: number;
  threadsGrowth: number;
  successRate: number;
  successTrend: 'up' | 'down';
  successChange: number;
  avgResponseTime: number;
  responseTrend: 'up' | 'down';
  responseChange: number;
  errors: number;
  errorTrend: 'up' | 'down';
  memoryUsage: number;
  cpuUsage: number;
  charts?: {
    threadActivity: Array<{ time: string; threads: number; runs: number }>;
    responseTime: Array<{ time: string; avgTime: number; p95Time: number }>;
    successRate: Array<{ time: string; success: number; errors: number }>;
  };
  performanceCharts?: {
    throughput: Array<{ time: string; requests: number; responses: number }>;
    latency: Array<{ time: string; min: number; avg: number; max: number; p95: number }>;
    errorBreakdown: Array<{ type: string; count: number; percentage: number }>;
  };
}

interface ThreadInfo {
  thread_id: string;
  status: 'idle' | 'busy' | 'interrupted' | 'error';
  created_at: string;
  updated_at: string;
  assistant_id?: string;
  message_count?: number;
  last_run_status?: string;
  metadata?: Record<string, any>;
}

interface ActivityEvent {
  id: string;
  timestamp: string;
  type: 'thread_created' | 'run_started' | 'run_completed' | 'run_failed' | 'message_sent';
  thread_id: string;
  details: Record<string, any>;
}

export const useLangGraphMonitoring = (timeRange = '24h') => {
  const queryClient = useQueryClient();
  const clientRef = useRef<Client>();

  // Initialize LangGraph client
  if (!clientRef.current) {
    clientRef.current = new Client({ apiUrl: LANGGRAPH_URL });
  }

  // Fetch threads data
  const { data: threads = [], isLoading: threadsLoading } = useQuery({
    queryKey: ['monitoring-threads', timeRange],
    queryFn: async (): Promise<ThreadInfo[]> => {
      if (!clientRef.current) return [];
      
      try {
        const threadsList = await clientRef.current.threads.search({ 
          limit: 100,
        });
        
        // Transform the data to include additional monitoring info
        return threadsList.map(thread => ({
          thread_id: thread.thread_id,
          status: thread.status || 'idle',
          created_at: thread.created_at || new Date().toISOString(),
          updated_at: thread.updated_at || new Date().toISOString(),
          assistant_id: (thread.metadata?.assistant_id as string) || LANGGRAPH_ASSISTANT_ID,
          message_count: 0, // Would need to fetch messages count
          last_run_status: 'completed',
          metadata: thread.metadata || {},
        }));
      } catch (error) {
        console.error('Failed to fetch threads:', error);
        return [];
      }
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 15000,
  });

  // Fetch metrics data
  const { data: metrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['monitoring-metrics', timeRange],
    queryFn: async (): Promise<MonitoringMetrics> => {
      // Simulate metrics calculation from threads data
      const now = new Date();
      const activeThreads = threads.filter(t => t.status === 'busy').length;
      
      // Generate mock chart data for demonstration
      const hours = timeRange === '1h' ? 1 : timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 720;
      const interval = timeRange === '1h' ? 5 : timeRange === '24h' ? 60 : timeRange === '7d' ? 360 : 1440; // minutes
      
      const chartData = [];
      for (let i = hours; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval * 60000);
        chartData.push({
          time: time.toISOString(),
          threads: Math.floor(Math.random() * 20) + 5,
          runs: Math.floor(Math.random() * 50) + 10,
          avgTime: Math.floor(Math.random() * 2000) + 500,
          p95Time: Math.floor(Math.random() * 5000) + 1000,
          success: Math.floor(Math.random() * 45) + 40,
          errors: Math.floor(Math.random() * 5) + 1,
        });
      }

      return {
        activeThreads,
        threadsGrowth: Math.floor(Math.random() * 20) - 10,
        successRate: 94.5,
        successTrend: 'up',
        successChange: 2.1,
        avgResponseTime: 1250,
        responseTrend: 'down',
        responseChange: -150,
        errors: 3,
        errorTrend: 'down',
        memoryUsage: 67,
        cpuUsage: 34,
        charts: {
          threadActivity: chartData.map(d => ({
            time: d.time,
            threads: d.threads,
            runs: d.runs,
          })),
          responseTime: chartData.map(d => ({
            time: d.time,
            avgTime: d.avgTime,
            p95Time: d.p95Time,
          })),
          successRate: chartData.map(d => ({
            time: d.time,
            success: d.success,
            errors: d.errors,
          })),
        },
        performanceCharts: {
          throughput: chartData.map(d => ({
            time: d.time,
            requests: d.runs,
            responses: d.runs - Math.floor(d.runs * 0.05),
          })),
          latency: chartData.map(d => ({
            time: d.time,
            min: Math.floor(d.avgTime * 0.3),
            avg: d.avgTime,
            max: Math.floor(d.avgTime * 2.5),
            p95: d.p95Time,
          })),
          errorBreakdown: [
            { type: 'Timeout', count: 2, percentage: 40 },
            { type: 'Rate Limit', count: 1, percentage: 20 },
            { type: 'Parse Error', count: 1, percentage: 20 },
            { type: 'Network', count: 1, percentage: 20 },
          ],
        },
      };
    },
    enabled: threads.length > 0,
    refetchInterval: 30000,
    staleTime: 15000,
  });

  // Mock activities data
  const { data: activities = [], isLoading: activitiesLoading } = useQuery({
    queryKey: ['monitoring-activities', timeRange],
    queryFn: async (): Promise<ActivityEvent[]> => {
      // Generate mock activity events
      const events: ActivityEvent[] = [];
      const now = new Date();
      
      for (let i = 0; i < 20; i++) {
        const timestamp = new Date(now.getTime() - i * 300000); // Every 5 minutes
        const threadId = `thread_${Math.floor(Math.random() * 10)}`;
        const eventTypes: ActivityEvent['type'][] = [
          'thread_created', 'run_started', 'run_completed', 'run_failed', 'message_sent'
        ];
        const type = eventTypes[Math.floor(Math.random() * eventTypes.length)];
        
        events.push({
          id: `event_${i}`,
          timestamp: timestamp.toISOString(),
          type,
          thread_id: threadId,
          details: {
            assistant_id: LANGGRAPH_ASSISTANT_ID,
            duration: type.includes('run') ? Math.floor(Math.random() * 5000) + 1000 : undefined,
            status: type === 'run_failed' ? 'error' : 'success',
          },
        });
      }
      
      return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    },
    refetchInterval: 15000,
    staleTime: 10000,
  });

  const refreshData = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['monitoring-threads'] });
    queryClient.invalidateQueries({ queryKey: ['monitoring-metrics'] });
    queryClient.invalidateQueries({ queryKey: ['monitoring-activities'] });
  }, [queryClient]);

  // Set up real-time updates
  useEffect(() => {
    const intervalId = setInterval(refreshData, 30000);
    return () => clearInterval(intervalId);
  }, [refreshData]);

  const isLoading = threadsLoading || metricsLoading || activitiesLoading;
  const error = !clientRef.current ? new Error('LangGraph client not initialized') : null;

  return {
    metrics,
    threads,
    activities,
    isLoading,
    error,
    refreshData,
  };
};