import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useCampaigns } from "@/hooks/useCampaigns";
import { toast } from "sonner";
import { Plus, FolderOpen, Calendar, Image } from "lucide-react";
import { useEffect } from "react";
import { createProjectUrl } from "@/utils/urlParams";

interface ProjectNewTitleModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProjectNewTitleModal = ({
  isOpen,
  onClose,
}: ProjectNewTitleModalProps) => {
  const [projectTitle, setProjectTitle] = useState("");
  const navigate = useNavigate();

  const handleCreateNewProject = () => {
    if (!projectTitle.trim()) {
      toast.error("Project title is required");
      return;
    }

    // Close modal and navigate to image generator with new project title
    onClose();
    const campaignUrl = createProjectUrl({
      mode: "new",
      projectTitle: projectTitle.trim(),
    });
    navigate(campaignUrl);
  };

  const handleCancel = () => {
    setProjectTitle("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <>
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Enter a title for your new image editing project.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="project-title">Project Title</Label>
              <Input
                id="project-title"
                value={projectTitle}
                onChange={(e) => setProjectTitle(e.target.value)}
                placeholder="Enter project title..."
                className="w-full"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateNewProject}
              disabled={!projectTitle.trim()}
            >
              Create
            </Button>
          </DialogFooter>
        </>
      </DialogContent>
    </Dialog>
  );
};
