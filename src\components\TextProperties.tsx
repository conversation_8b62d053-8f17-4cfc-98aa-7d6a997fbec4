import { useState, useEffect, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Type, Bold, Italic, Underline, Palette, Trash2, ChevronDown, ChevronRight, AlignLeft, AlignCenter, AlignRight, AlignJustify } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Textarea } from "./ui/textarea";

interface TextEditorProps {
  // onAddText moved to header toolbar; kept optional for backward compatibility (unused here)
  onAddText?: (textOptions: TextOptions) => void;
  onUpdateText?: (textOptions: TextOptions) => void;
  onDeleteText?: () => void;
  selectedTextOptions?: TextOptions | null;
}

export interface TextOptions {
  text: string;
  fontFamily: string;
  fontSize: number;
  fontWeight: string;
  fontStyle: string;
  textDecoration: string;
  fill: string;
  background?: {
    enabled: boolean;
    color: string;
  };
  stroke?: string;
  strokeWidth?: number;
  shadow?: {
    blur: number;
    offsetX: number;
    offsetY: number;
    color: string;
  };
  // Alignment (default left)
  textAlign?: TextAlign;

  // Optional metadata so other components (like ProjectMain) can track selection index
  meta?: {
    index?: number;
  };
}

  type TextAlign = 'left' | 'center' | 'right' | 'justify';


export const TextProperties = ({ onUpdateText, onDeleteText, selectedTextOptions }: TextEditorProps) => {
  const [text, setText] = useState("");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontSize, setFontSize] = useState([32]);
  const [fontWeight, setFontWeight] = useState("normal");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textDecoration, setTextDecoration] = useState("normal");
  const [fill, setFill] = useState("#000000"); // Changed to black for visibility
  const [textAlign, setTextAlign] = useState<TextAlign>('left');
  const [stroke, setStroke] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState([0]);
  const [shadowBlur, setShadowBlur] = useState([0]);
  const [shadowOffsetX, setShadowOffsetX] = useState([2]);
  // Background behind text (transparent by default)
  const [bgEnabled, setBgEnabled] = useState(false);
  const [bgColor, setBgColor] = useState<string>("#ffffff");

  // Flag to prevent real-time updates when populating form fields from selection
  const [isPopulatingFromSelection, setIsPopulatingFromSelection] = useState(false);
  // Ref to skip the very next real-time update after a selection change
  const suppressNextUpdateRef = useRef(false);
  const [shadowOffsetY, setShadowOffsetY] = useState([2]);
  const [shadowColor, setShadowColor] = useState("#000000");

  // Collapsible sections state
  const [isTypographyOpen, setIsTypographyOpen] = useState(true);
  const [isStrokeOpen, setIsStrokeOpen] = useState(false);
  const [isBackgroundOpen, setIsBackgroundOpen] = useState(true);
  const [isEffectsOpen, setIsEffectsOpen] = useState(false);

	  const isDisabled = !selectedTextOptions;


  const fontCategories = {
    "Sans-serif": [
      { name: "Arial", value: "Arial" },
      { name: "Helvetica", value: "Helvetica" },
      { name: "Roboto", value: "Roboto" },
      { name: "Open Sans", value: "Open Sans" },
      { name: "Lato", value: "Lato" },
      { name: "Montserrat", value: "Montserrat" },
      { name: "Source Sans Pro", value: "Source Sans Pro" },
      { name: "Nunito", value: "Nunito" },
      { name: "Verdana", value: "Verdana" },
      { name: "Trebuchet MS", value: "Trebuchet MS" }
    ],
    "Serif": [
      { name: "Times New Roman", value: "Times New Roman" },
      { name: "Georgia", value: "Georgia" },
      { name: "Playfair Display", value: "Playfair Display" },
      { name: "Merriweather", value: "Merriweather" },
      { name: "Lora", value: "Lora" },
      { name: "Crimson Text", value: "Crimson Text" },
      { name: "Palatino", value: "Palatino" }
    ],
    "Display": [
      { name: "Oswald", value: "Oswald" },
      { name: "Bebas Neue", value: "Bebas Neue" },
      { name: "Abril Fatface", value: "Abril Fatface" },
      { name: "Righteous", value: "Righteous" },
      { name: "Impact", value: "Impact" }
    ],
    "Handwriting": [
      { name: "Dancing Script", value: "Dancing Script" },
      { name: "Pacifico", value: "Pacifico" },
      { name: "Satisfy", value: "Satisfy" },
      { name: "Comic Sans MS", value: "Comic Sans MS" }
    ],
    "Monospace": [
      { name: "Fira Code", value: "Fira Code" },
      { name: "Source Code Pro", value: "Source Code Pro" },
      { name: "Courier New", value: "Courier New" }
    ]
  };

  // Flatten all fonts for the select component
  const allFonts = Object.values(fontCategories).flat();

  const presetColors = [
    "#ffffff", "#000000", "#ff0000", "#00ff00", "#0000ff",
    "#ffff00", "#ff00ff", "#00ffff", "#ffa500", "#800080"
  ];

  // Populate form fields when a text object is selected
  useEffect(() => {
    if (selectedTextOptions) {
      // Immediately suppress the next real-time update before any state changes to prevent
      // copying the previous selection's properties onto the newly selected object.
      suppressNextUpdateRef.current = true;

      // Set flag to prevent real-time updates during population
      setIsPopulatingFromSelection(true);

      setText(selectedTextOptions.text);
      setFontFamily(selectedTextOptions.fontFamily);
      setFontSize([selectedTextOptions.fontSize]);
      setFontWeight(selectedTextOptions.fontWeight);
      setFontStyle(selectedTextOptions.fontStyle);
      setTextDecoration(selectedTextOptions.textDecoration);
      setFill(selectedTextOptions.fill);
      if (selectedTextOptions.stroke) {
        setStroke(selectedTextOptions.stroke);
      }
      if (selectedTextOptions.strokeWidth !== undefined) {
        setStrokeWidth([selectedTextOptions.strokeWidth]);
      }
      if (selectedTextOptions.shadow) {
        setShadowBlur([selectedTextOptions.shadow.blur]);
        setShadowOffsetX([selectedTextOptions.shadow.offsetX]);
        setShadowOffsetY([selectedTextOptions.shadow.offsetY]);
        setShadowColor(selectedTextOptions.shadow.color);
      } else {
        setShadowBlur([0]);
        setShadowOffsetX([2]);
        setShadowOffsetY([2]);
        setShadowColor("#000000");
      }

      // Alignment
      setTextAlign((selectedTextOptions as any).textAlign || 'left');

      // Background (default transparent via disabled)
      if (selectedTextOptions.background) {
        setBgEnabled(!!selectedTextOptions.background.enabled);
        setBgColor(selectedTextOptions.background.color || "#ffffff");
      } else {
        setBgEnabled(false);
        setBgColor("#ffffff");
      }

      // Clear the flag after a brief delay to allow all state updates to complete
      setTimeout(() => {
        setIsPopulatingFromSelection(false);
      }, 0);
    }
  }, [selectedTextOptions]);

  // createDefaultTextOptions retained for future use by parent if needed
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const createDefaultTextOptions = (): TextOptions => ({
    text,
    fontFamily,
    fontSize: fontSize[0],
    fontWeight,
    fontStyle,
    textDecoration,
    fill,
    background: { enabled: bgEnabled, color: bgColor },
    stroke: strokeWidth[0] > 0 ? stroke : undefined,
    strokeWidth: strokeWidth[0] > 0 ? strokeWidth[0] : undefined,
    shadow: shadowBlur[0] > 0 ? {
      blur: shadowBlur[0],
      offsetX: shadowOffsetX[0],
      offsetY: shadowOffsetY[0],
      color: shadowColor
    } : undefined,
    // alignment
    ...(textAlign ? { textAlign } : {})
  });

  // Apply real-time updates when properties change
  const applyRealTimeUpdate = useCallback(() => {
    // Don't apply updates if we're currently populating from selection
    if (selectedTextOptions && onUpdateText && !isPopulatingFromSelection) {
      const textOptions: TextOptions = {
        text,
        fontFamily,
        fontSize: fontSize[0],
        fontWeight,
        fontStyle,
        textDecoration,
        fill,
        background: { enabled: bgEnabled, color: bgColor },
        stroke: strokeWidth[0] > 0 ? stroke : undefined,
        strokeWidth: strokeWidth[0] > 0 ? strokeWidth[0] : undefined,
        shadow: shadowBlur[0] > 0 ? {
          blur: shadowBlur[0],
          offsetX: shadowOffsetX[0],
          offsetY: shadowOffsetY[0],
          color: shadowColor
        } : undefined,
        textAlign
      };
      onUpdateText(textOptions);
    }
  }, [selectedTextOptions, onUpdateText, isPopulatingFromSelection, text, fontFamily, fontSize, fontWeight, fontStyle, textDecoration, fill, stroke, strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, shadowColor, textAlign, bgEnabled, bgColor]);

  // Trigger real-time updates when properties change (but only if text is selected and not populating from selection)
  useEffect(() => {
    if (selectedTextOptions && !isPopulatingFromSelection) {
      if (suppressNextUpdateRef.current) {
        // Skip this update once after selection change, then re-enable
        suppressNextUpdateRef.current = false;
      } else {
        applyRealTimeUpdate();
      }
    }
  }, [selectedTextOptions, isPopulatingFromSelection, text, fontFamily, fontSize, fontWeight, fontStyle, textDecoration, fill, stroke, strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, shadowColor, textAlign, bgEnabled, bgColor, applyRealTimeUpdate]);

  // Add Text button moved to header toolbar in ProjectMain. This component no longer adds text directly.

  return (
    <Card className={`p-3 space-y-3 bg-card border-border h-full overflow-y-auto min-h-0 ${isDisabled ? 'opacity-60 pointer-events-none' : ''}`}>
      <div className="flex items-center gap-2 mb-1">
        <Type className="h-4 w-4 text-primary" />
        <h3 className="text-base font-semibold">Text Properties {}</h3>
      </div>

      {!selectedTextOptions && (
        <div className="p-2 text-xs text-muted-foreground border border-dashed rounded-md">
          No text selected. Click a text item on the canvas or in the list to edit its properties.
        </div>
      )}

      {/* Text Input - Always visible */}
      <div className="space-y-3">
        <Label htmlFor="text-input">Text</Label>
        <Textarea
          id="text-input"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Here your text"
          className="bg-background"
          disabled={isDisabled}
        />
      </div>

      {/* Typography Section */}
      <Collapsible className="bg-slate-100/40" open={isTypographyOpen} onOpenChange={setIsTypographyOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-1.5 hover:bg-muted/50 rounded-md transition-colors">
          <div className="flex items-center gap-2">
            <Type className="h-3.5 w-3.5" />
            <span className="text-sm font-medium">Typography</span>
          </div>
          {isTypographyOpen ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRight className="h-3.5 w-3.5" />}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          {/* Font Family */}
          <div className="space-y-2">
            <Label>Font Family</Label>
            <Select value={fontFamily} onValueChange={setFontFamily} disabled={isDisabled}>
              <SelectTrigger className="bg-background" disabled={isDisabled}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(fontCategories).map(([category, fonts]) => (
                  <div key={category}>
                    <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground bg-muted/50">
                      {category}
                    </div>
                    {fonts.map((font) => (
                      <SelectItem 
                        key={font.value} 
                        value={font.value} 
                        style={{ fontFamily: font.value }}
                        className="pl-4"
                      >
                        {font.name}
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Font Size */}
          <div className="space-y-2">
            <Label>Font Size: {fontSize[0]}px</Label>
            <Slider
              value={fontSize}
              onValueChange={setFontSize}
              min={12}
              max={120}
              step={1}
              className="w-full"
              disabled={isDisabled}
            />
          </div>

          {/* Font Style Controls */}
          <div className="space-y-2">
            <Label>Style</Label>
            <div className="flex gap-2">
              <Button
                variant={fontWeight === "bold" ? "tool-active" : "tool"}
                size="tool"
                onClick={() => setFontWeight(fontWeight === "bold" ? "normal" : "bold")}
                disabled={isDisabled}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                variant={fontStyle === "italic" ? "tool-active" : "tool"}
                size="tool"
                onClick={() => setFontStyle(fontStyle === "italic" ? "normal" : "italic")}
                disabled={isDisabled}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                variant={textDecoration === "underline" ? "tool-active" : "tool"}
                size="tool"
                onClick={() => setTextDecoration(textDecoration === "underline" ? "normal" : "underline")}
                disabled={isDisabled}
              >
                <Underline className="h-4 w-4" />
              </Button>
            </div>
          </div>

	          {/* Alignment Controls */}
	          <div className="space-y-2">
	            <Label>Alignment</Label>
	            <div className="flex gap-2">
	              <Button
	                variant={textAlign === 'left' ? 'tool-active' : 'tool'}
	                size="tool"
	                onClick={() => setTextAlign('left')}
	                disabled={isDisabled}
	                title="Align Left"
	              >
	                <AlignLeft className="h-4 w-4" />
	              </Button>
	              <Button
	                variant={textAlign === 'center' ? 'tool-active' : 'tool'}
	                size="tool"
	                onClick={() => setTextAlign('center')}
	                disabled={isDisabled}
	                title="Align Center"
	              >
	                <AlignCenter className="h-4 w-4" />
	              </Button>
	              <Button
	                variant={textAlign === 'right' ? 'tool-active' : 'tool'}
	                size="tool"
	                onClick={() => setTextAlign('right')}
	                disabled={isDisabled}
	                title="Align Right"
	              >
	                <AlignRight className="h-4 w-4" />
	              </Button>
	              <Button
	                variant={textAlign === 'justify' ? 'tool-active' : 'tool'}
	                size="tool"
	                onClick={() => setTextAlign('justify')}
	                disabled={isDisabled}
	                title="Justify"
	              >
	                <AlignJustify className="h-4 w-4" />
	              </Button>
	            </div>
	          </div>


          {/* Text Color */}
          <div className="space-y-2">
            <Label>Text Color</Label>
            <div className="flex flex-wrap items-center gap-2">
              <input
                type="color"
                value={fill}
                onChange={(e) => setFill(e.target.value)}
                className="w-8 h-8 rounded border border-border cursor-pointer"
                disabled={isDisabled}
              />
              <div className="flex flex-wrap gap-1">
                {presetColors.map((color) => (
                  <button
                    key={color}
                    onClick={() => setFill(color)}
                    className="w-6 h-6 rounded border border-border cursor-pointer hover:scale-110 transition-transform disabled:opacity-50"
                    style={{ backgroundColor: color }}
                    disabled={isDisabled}
                  />
                ))}
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Stroke Section */}
      <Collapsible className="bg-slate-100/40" open={isStrokeOpen} onOpenChange={setIsStrokeOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-1.5 hover:bg-muted/50 rounded-md transition-colors">
          <div className="flex items-center gap-2">
            <Underline className="h-3.5 w-3.5" />
            <span className="text-sm font-medium">Stroke</span>
          </div>
          {isStrokeOpen ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRight className="h-3.5 w-3.5" />}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="space-y-2">
            <Label>Stroke Width: {strokeWidth[0]}px</Label>
            <Slider
              value={strokeWidth}
              onValueChange={setStrokeWidth}
              min={0}
              max={10}
              step={1}
              className="w-full"
              disabled={isDisabled}
            />
            {strokeWidth[0] > 0 && (
              <div className="flex items-center gap-2">
                <Label className="text-sm">Stroke Color:</Label>
                <input
                  type="color"
                  value={stroke}
                  onChange={(e) => setStroke(e.target.value)}
                  className="w-6 h-6 rounded border border-border cursor-pointer"
                  disabled={isDisabled}
                />
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Background Section */}
      <Collapsible className="bg-slate-100/40" open={isBackgroundOpen} onOpenChange={setIsBackgroundOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-1.5 hover:bg-muted/50 rounded-md transition-colors">
          <div className="flex items-center gap-2">
            <Palette className="h-3.5 w-3.5" />
            <span className="text-sm font-medium">Background</span>
          </div>
          {isBackgroundOpen ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRight className="h-3.5 w-3.5" />}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Enable Background</Label>
              <label className="flex items-center gap-2 text-sm">
                <input type="checkbox" checked={bgEnabled} onChange={(e) => setBgEnabled(e.target.checked)} disabled={isDisabled} />
                Enable
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={bgColor}
                onChange={(e) => setBgColor(e.target.value)}
                className="w-8 h-8 rounded border border-border cursor-pointer"
                disabled={!bgEnabled}
              />
              <div className="flex flex-wrap gap-1">
                {presetColors.map((color) => (
                  <button
                    key={color}
                    onClick={() => { setBgColor(color); setBgEnabled(true); }}
                    className="w-6 h-6 rounded border border-border cursor-pointer hover:scale-110 transition-transform disabled:opacity-50"
                    style={{ backgroundColor: color }}
                    disabled={!bgEnabled}
                  />
                ))}
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Effects Section */}
      <Collapsible  className="bg-slate-100/40" open={isEffectsOpen} onOpenChange={setIsEffectsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-1.5 hover:bg-muted/50 rounded-md transition-colors">
          <div className="flex items-center gap-2">
            <Palette className="h-3.5 w-3.5" />
            <span className="text-sm font-medium">Effects</span>
          </div>
          {isEffectsOpen ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRight className="h-3.5 w-3.5" />}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="space-y-2">
            <Label>Shadow Blur: {shadowBlur[0]}px</Label>
            <Slider
              value={shadowBlur}
              onValueChange={setShadowBlur}
              min={0}
              max={20}
              step={1}
              className="w-full"
              disabled={isDisabled}
            />

            {shadowBlur[0] > 0 && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Shadow X: {shadowOffsetX[0]}px</Label>
                    <Slider
                      value={shadowOffsetX}
                      onValueChange={setShadowOffsetX}
                      min={-10}
                      max={10}
                      step={1}
                      className="w-full"
                      disabled={isDisabled}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Shadow Y: {shadowOffsetY[0]}px</Label>
                    <Slider
                      value={shadowOffsetY}
                      onValueChange={setShadowOffsetY}
                      min={-10}
                      max={10}
                      step={1}
                      className="w-full"
                      disabled={isDisabled}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Label className="text-sm">Shadow Color:</Label>
                  <input
                    type="color"
                    value={shadowColor}
                    onChange={(e) => setShadowColor(e.target.value)}
                    className="w-6 h-6 rounded border border-border cursor-pointer"
                    disabled={isDisabled}
                  />
                </div>
              </>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Selected Text Status & Delete Action */}
      {selectedTextOptions && (
        <div className="space-y-3">
          <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-primary">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <span className="font-medium">Editing Selected Text</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Changes are applied in real-time
            </p>
          </div>

          {/* {onDeleteText && (
            <Button
              onClick={onDeleteText}
              variant="destructive"
              size="lg"
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected Text1
            </Button>
          )} */}
        </div>
      )}
    </Card>
  );
};
