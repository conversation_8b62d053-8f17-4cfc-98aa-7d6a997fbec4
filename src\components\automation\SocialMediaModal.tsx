import React, { useEffect, useMemo, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Share2, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface SocialMediaModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageDataUrl: string | null;
  projectId?: string | null;
}

const PLATFORM_LIST = [
  { id: 'instagram', label: 'Instagram' },
  { id: 'tiktok', label: 'TikTok' },
  { id: 'facebook', label: 'Facebook' },
] as const;

const SocialMediaModal: React.FC<SocialMediaModalProps> = ({ isOpen, onClose, imageDataUrl, projectId }) => {
  const [caption, setCaption] = useState<string>('');
  const [selected, setSelected] = useState<Record<string, boolean>>({});
  const [isPublishing, setIsPublishing] = useState(false);

  function addTimestampToFilename(filename: string): string {
    const timestamp = new Date().toISOString().replace(/[-:.TZ]/g, ""); // milliseconds since epoch
    const dotIndex = filename.lastIndexOf(".");

    if (dotIndex === -1) {
      // No extension
      return `${filename}_${timestamp}`;
    }

    const name = filename.substring(0, dotIndex);
    const extension = filename.substring(dotIndex);
    return `${name}_${timestamp}${extension}`;
}
  // Reset form when opening/closing
  useEffect(() => {
    if (!isOpen) {
      setCaption('');
      setSelected({});
      setIsPublishing(false);
    }
  }, [isOpen]);

  const selectedPlatforms = useMemo(
    () => PLATFORM_LIST.filter(p => selected[p.id]).map(p => p.id),
    [selected]
  );

  const canPublish = !!imageDataUrl && caption.trim().length > 0 && selectedPlatforms.length > 0 && !isPublishing;

  const handlePublish = async () => {
    if (!canPublish) return;

    try {
      setIsPublishing(true);

      // Convert base64 data URL to blob
      const response = await fetch(imageDataUrl!);
      const imageBlob = await response.blob();
      const imageFileName = addTimestampToFilename('image.png');
      // Create FormData to send blob
      const formData = new FormData();
      formData.append('platforms', JSON.stringify(selectedPlatforms));
      formData.append('caption', caption.trim());
      formData.append('data', imageBlob, imageFileName);
      if (projectId) {
        formData.append('projectId', projectId);
      }

      const res = await fetch('https://genenrativepangea.app.n8n.cloud/webhook-test/catchup/social/publish', {
        method: 'POST',
        body: formData, // No Content-Type header needed - browser will set it automatically with boundary
      });

      if (!res.ok) {
        const text = await res.text().catch(() => '');
        throw new Error(text || `Request failed with ${res.status}`);
      }

      toast.success('Publish request sent successfully');
      onClose();
    } catch (err: any) {
      console.error('Publish error:', err);
      toast.error(`Failed to publish: ${err?.message || 'Unknown error'}`);
    } finally {
      setIsPublishing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Social Media Publishing
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Image Preview */}
          {imageDataUrl ? (
            <div className="flex justify-center">
              <img src={imageDataUrl} alt="Rendered image" className="max-h-64 rounded-lg border shadow-sm" />
            </div>
          ) : (
            <div className="text-center text-sm text-muted-foreground">Image preview unavailable (CORS or not ready)</div>
          )}

          {/* Platforms */}
          <div>
            <Label className="mb-2 block">Select platforms</Label>
            <div className="flex flex-wrap gap-4">
              {PLATFORM_LIST.map(p => (
                <label key={p.id} className="flex items-center gap-2 cursor-pointer select-none">
                  <Checkbox
                    checked={!!selected[p.id]}
                    onCheckedChange={(v) => setSelected(prev => ({ ...prev, [p.id]: Boolean(v) }))}
                  />
                  <span>{p.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Caption */}
          <div>
            <Label htmlFor="caption" className="mb-2 block">Caption</Label>
            <Textarea
              id="caption"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              placeholder="Write a catchy caption..."
              className="min-h-[80px]"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose} disabled={isPublishing}>Cancel</Button>
            <Button onClick={handlePublish} disabled={!canPublish} className="flex items-center gap-2">
              {isPublishing && <Loader2 className="h-4 w-4 animate-spin" />}
              Publish
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SocialMediaModal;

