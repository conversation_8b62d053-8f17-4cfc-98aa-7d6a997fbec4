// src/features/chat/Chat.tsx
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useStream } from "@langchain/langgraph-sdk/react";
import type { Message } from "@langchain/langgraph-sdk";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
// UI
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

import { DeleteIcon, Loader2, Trash2Icon } from "lucide-react";
import {
  Suggestion,
  MessageInput,
} from "@/components/ui/pangea-ui/ai/messageinput";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { ChevronsUpDown, RefreshCcw, Plus } from "lucide-react";

import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

export function MarkdownText({ text }: { text: string }) {
  return (
    <div className="max-w-none space-y-3">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          pre({ children }) {
            const raw = String((children as any)?.props?.children ?? "");
            const onCopy = () => navigator.clipboard.writeText(raw);
            return (
              <div className="group relative">
                <pre className="overflow-x-auto rounded-lg bg-muted p-3 text-sm leading-6">
                  {children}
                </pre>
                <button
                  type="button"
                  onClick={onCopy}
                  className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 text-xs border rounded px-2 py-1"
                >
                  Copy
                </button>
              </div>
            );
          },
          code({ className, children, ...props }) {
            return (
              <code
                {...props}
                className="rounded bg-muted px-1 py-0.5 text-[0.85em]"
              >
                {children}
              </code>
            );
          },
        }}
      >
        {text}
      </ReactMarkdown>
    </div>
  );
}

type State = { messages: Message[] };
const LANGGRAPH_LOCAL_URL = "http://127.0.0.1:2024";

const LANGGRAPH_REMOTE_URL = "https://lancatchup.onrender.com";
const LANGGRAPH_ASSISTANT_ID = "catchup";

const BASE_STATE = {
  user_id: "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
  latitude: "45.4666",
  longitude: "9.1832",
  memory_lenght: 15, // keep the exact key used in your graph state
  email_address: "<EMAIL>",
  mobile_number: "41787360140",
  language: "IT-it",
  mode: "voice",
};

function cn(...cls: Array<string | false | null | undefined>) {
  return cls.filter(Boolean).join(" ");
}

// --- utils ---
function safeParseJSON(input?: unknown) {
  if (typeof input !== "string") return null;
  try {
    return JSON.parse(input);
  } catch {
    return null;
  }
}

function timeAgo(iso?: string) {
  if (!iso) return "";
  const seconds = Math.floor((Date.now() - new Date(iso).getTime()) / 1000);
  const rtf = new Intl.RelativeTimeFormat(undefined, { numeric: "auto" });

  const units: [Intl.RelativeTimeFormatUnit, number][] = [
    ["year", 31557600], // 365.25 d
    ["month", 2629800], // ~30.44 d
    ["week", 604800],
    ["day", 86400],
    ["hour", 3600],
    ["minute", 60],
    ["second", 1],
  ];

  for (const [unit, s] of units) {
    const value = Math.floor(seconds / s);
    if (Math.abs(value) >= 1) return rtf.format(-value, unit);
  }
  return rtf.format(0, "second");
}

function timeAgoOnlyIfOver(iso?: string, thresholdMs = 24 * 60 * 60 * 1000) {
  if (!iso) return "";
  const ms = Date.now() - new Date(iso).getTime();
  return ms >= thresholdMs ? timeAgo(iso) : "";
}

function displayWhen(iso?: string) {
  if (!iso) return "";
  const d = new Date(iso);
  const ms = Date.now() - d.getTime();
  return ms < 24 * 60 * 60 * 1000
    ? d.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    : timeAgo(iso);
}

function pretty(value: any) {
  const txt =
    typeof value === "string" ? value : JSON.stringify(value, null, 2);
  return txt;
}

// Estrarre tool calls robustamente da un AIMessage
type ToolCall = { id?: string; name?: string; args?: any };

function getToolCalls(m: any): ToolCall[] {
  const calls = m?.tool_calls ?? m?.additional_kwargs?.tool_calls;
  if (!Array.isArray(calls)) return [];
  return calls.map((c: any) => ({
    id: c.id ?? c.tool_call_id ?? c.call_id,
    name: c.name ?? c.function?.name ?? c.tool_name,
    args:
      typeof c.args === "string"
        ? safeParseJSON(c.args) ?? c.args
        : c.args ??
          safeParseJSON(c.function?.arguments) ??
          c.function?.arguments,
  }));
}

// --- UI cards (shadcn-friendly, niente import extra) ---
function isEmptyRecord(x: any) {
  return (
    x &&
    typeof x === "object" &&
    !Array.isArray(x) &&
    Object.keys(x).length === 0
  );
}

function ToolCallCard({ call }: { call: ToolCall }) {
  const hasArgs =
    call.args != null &&
    !(typeof call.args === "string" && call.args.trim() === "") &&
    !(typeof call.args === "object" && isEmptyRecord(call.args));

  return (
    <div className="mt-2 rounded-xl border bg-background">
      <div className="flex items-center justify-between px-3 py-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-2">
          <span className="inline-flex items-center rounded bg-muted px-1.5 py-0.5">
            call_model
          </span>
          <span className="font-medium">{call.name ?? "tool_call"}</span>
        </div>
        {call.id ? <span className="opacity-70">ID {call.id}</span> : null}
      </div>
      {hasArgs ? (
        <pre className="px-3 pb-3 text-xs whitespace-pre-wrap">
          {pretty(call.args)}
        </pre>
      ) : null}
    </div>
  );
}

function ToolResultCard({ name, result }: { name?: string; result: any }) {
  const body =
    typeof result === "string" ? result : JSON.stringify(result, null, 2);
  const collapsed = body.length > 800; // tweak threshold

  return (
    <div className="mt-2 rounded-xl border bg-muted/40">
      <div className="flex items-center gap-2 px-3 py-2 text-xs text-muted-foreground">
        <span className="inline-flex items-center rounded bg-muted px-1.5 py-0.5">
          tools
        </span>
        <span>
          {name ? (
            <>
              Tool result: <span className="font-medium">{name}</span>
            </>
          ) : (
            "Tool result"
          )}
        </span>
      </div>

      {collapsed ? (
        <details>
          <summary className="cursor-pointer px-3 pb-1 text-xs text-muted-foreground">
            Show result
          </summary>
          <pre className="px-3 pb-3 text-xs whitespace-pre-wrap">{body}</pre>
        </details>
      ) : (
        <pre className="px-3 pb-3 text-xs whitespace-pre-wrap">{body}</pre>
      )}
    </div>
  );
}

// Renders string content OR LangChain-style content blocks (text, image_url)
function RenderContent({ content }: { content: unknown }) {
  if (typeof content === "string") {
    const maybe = safeParseJSON(content);
    // se il model ti manda un JSON come quello nello screenshot, estrai il campo "llmResponse"/"ilmResponse"
    const resp = (maybe && (maybe.llmResponse || maybe.ilmResponse)) as
      | string
      | undefined;
    if (resp) return <MarkdownText text={resp} />;
    // altrimenti, se è un JSON generico, mostrane la forma leggibile
    if (maybe) {
      return (
        <div className="space-y-2">
          <MarkdownText
            text={"```json\n" + JSON.stringify(maybe, null, 2) + "\n```"}
          />
        </div>
      );
    }
    return <MarkdownText text={content} />;
  }

  if (Array.isArray(content)) {
    return (
      <div className="space-y-2">
        {(content as any[]).map((c, i) => {
          //if (c?.type === "text") return <span key={i}>{c.text}</span>;
          if (c?.type === "text")
            return <MarkdownText key={i} text={c.text ?? ""} />;
          if (c?.type === "image_url") {
            const url =
              typeof c.image_url === "string" ? c.image_url : c.image_url?.url;
            return (
              <img
                key={i}
                src={url}
                alt="attachment"
                className="max-w-full rounded-lg border"
              />
            );
          }
          return (
            <pre
              key={i}
              className="text-xs bg-muted/50 text-muted-foreground rounded-md p-2"
            >
              {JSON.stringify(c, null, 2)}
            </pre>
          );
        })}
      </div>
    );
  }
  return (
    <pre className="text-xs bg-muted/50 text-muted-foreground rounded-md p-2">
      {JSON.stringify(content, null, 2)}
    </pre>
  );
}

function ChatBubble({
  message,
  isUser,
}: {
  message: Message;
  isUser: boolean;
}) {
  return (
    <div
      className={cn(
        "flex w-full gap-3",
        isUser ? "justify-end" : "justify-start"
      )}
    >
      {!isUser && (
        <Avatar className="h-8 w-8">
          <AvatarImage src="" alt="AI" />
          <AvatarFallback>AI</AvatarFallback>
        </Avatar>
      )}

      <div
        className={cn(
          "max-w-[80ch] rounded-2xl px-3 py-2 whitespace-pre-wrap",
          isUser
            ? "bg-primary text-primary-foreground"
            : "bg-muted text-foreground"
        )}
      >
        <RenderContent content={message.content as any} />
      </div>

      {isUser && (
        <Avatar className="h-8 w-8">
          <AvatarImage src="" alt="You" />
          <AvatarFallback>U</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}

function ChatInner({
  threadId,
  onThreadId,
}: {
  threadId?: string;
  onThreadId: (id?: string) => void;
}) {
  const [lastRunByThread, setLastRunByThread] = useState<
    Record<string, { assistant_id?: string; created_at?: string }>
  >({});

  const [langraphServer, setLangraphServer] =
    useState<string>(LANGGRAPH_LOCAL_URL);
  // Debug toggle (default ON to keep your current look)
  const [showDebug, setShowDebug] = useState(true);
  // Streamed tool events from the backend writer()
  type ToolEvt = {
    event?: string;
    tool?: string;
    id?: string;
    args?: any;
    at?: number;
  };
  const [toolEvents, setToolEvents] = useState<ToolEvt[]>([]);

  // const thread = useStream<State>({
  //   apiUrl: LANGGRAPH_URL, //import.meta.env.VITE_LANGGRAPH_URL,
  //   assistantId: LANGGRAPH_ASSISTANT_ID, //import.meta.env.VITE_ASSISTANT_ID,
  //   // apiKey: import.meta.env.VITE_LANGGRAPH_API_KEY, // optional
  //   messagesKey: "messages",
  //   threadId,
  //   onThreadId,
  //   reconnectOnMount: true,
  // });

  const thread = useStream<State>({
    apiUrl: langraphServer,
    assistantId: LANGGRAPH_ASSISTANT_ID,
    messagesKey: "messages",
    threadId,
    onThreadId,
    reconnectOnMount: true,

    // NEW — capture events from get_stream_writer()
    onCustomEvent: (data: any) => {
      setToolEvents((prev) => [...prev, { ...(data ?? {}), at: Date.now() }]);
    },
    // clear ephemeral events between runs
    onFinish: () => setToolEvents([]),
    onStop: () => setToolEvents([]),
  });

  type ThreadRow = {
    thread_id: string;
    status?: "idle" | "busy" | "interrupted" | "error";
    created_at?: string;
    updated_at?: string;
    metadata?: Record<string, any>;
  };
  // threads list state
  const [threadsList, setThreadsList] = useState<ThreadRow[]>([]);
  const [loadingThreads, setLoadingThreads] = useState(false);

  const fetchThreads = useCallback(async () => {
    setLoadingThreads(true);
    try {
      // You can add filters like { status: "idle" } or metadata if you tag threads
      const list = await thread.client.threads.search({ limit: 25 });
      setThreadsList(list);
    } finally {
      setLoadingThreads(false);
    }
  }, [thread.client]);

  useEffect(() => {
    void fetchThreads();
  }, [fetchThreads]);

  const deleteThread = useCallback(async () => {
    if (!threadId) return;
    if (!window.confirm("Are you sure you want to delete this thread?")) return;
    await thread.client.threads.delete(threadId);
    onThreadId(undefined);
    void fetchThreads();
  }, [thread.client, threadId, fetchThreads, onThreadId]);

  const deleteAllThreads = useCallback(async () => {
    if (!window.confirm("Are you sure you want to delete all threads?")) return;
    const t = await thread.client.threads.search({ limit: 100 });
    await Promise.all(t.map((t) => thread.client.threads.delete(t.thread_id)));
    setThreadsList([]);
    onThreadId(undefined);
    void fetchThreads();
  }, [thread.client, fetchThreads, onThreadId]);

  const createNewThread = useCallback(async () => {
    const t =
      await thread.client.threads.create(/* { metadata: { user_id: BASE_STATE.user_id } } */);
    onThreadId(t.thread_id);
    void fetchThreads();
  }, [thread.client, fetchThreads, onThreadId]);

  // auto-scroll to bottom on new messages
  const bottomRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [thread.messages.length, thread.isLoading]);
  const test_question =
    "Ciao, puoi prendere appuntamento dal barbiere per un taglio di capeli e barba per domani nel pomeriggio. E dopo mi prenoti un aperitivo per due. Grazie.";
  const suggestions: Suggestion[] = useMemo(
    () => [
      { text: test_question },
      {
        text: "Vorrei organizzare una weeekend romantico. Che mi consigli?",
      },
      { text: "Mi dai l'elenco delle mie prenotazioni?" },
      { text: "Mandami tutto su whatsapp." },
    ],
    []
  );

  const sendText = useCallback(
    async (text: string) => {
      await thread.submit({
        ...BASE_STATE,
        messages: [{ type: "human", content: text }],
      });
    },
    [thread]
  );

  // simple image attachment via data URL (quick POC)
  const fileRef = useRef<HTMLInputElement | null>(null);
  const onAttachClick = () => fileRef.current?.click();
  const onFilePicked = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const buf = await file.arrayBuffer();
    const base64 = btoa(String.fromCharCode(...new Uint8Array(buf)));
    const dataUrl = `data:${file.type};base64,${base64}`;

    await thread.submit({
      ...BASE_STATE,
      messages: [
        {
          type: "human",
          content: [
            { type: "text", text: "Please analyze this." },
            { type: "image_url", image_url: { url: dataUrl } },
          ],
        },
      ],
    });
    if (fileRef.current) fileRef.current.value = "";
  };

  function onLangraphServerChange(LANGGRAPH_LOCAL_URL: string): void {
    setLangraphServer(LANGGRAPH_LOCAL_URL);
  
    setThreadsList([]);
    setLastRunByThread({});
    setToolEvents([]);
    setLoadingThreads(false);
    thread.stop();
    
  }

  return (
    <div className="flex h-dvh max-h-dvh flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 border-b">
        <div className="font-medium">CatchUp</div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">Change Server</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onLangraphServerChange(LANGGRAPH_LOCAL_URL)} >
                {LANGGRAPH_LOCAL_URL}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onLangraphServerChange(LANGGRAPH_REMOTE_URL)}>
                {LANGGRAPH_REMOTE_URL}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          Server: {langraphServer}
        </div>
        {/* <ThreadHeaderControls
          threadId={threadId}
          threadsList={threadsList}
          lastRunByThread={lastRunByThread}
          loadingThreads={loadingThreads}
          showDebug={showDebug}
          setShowDebug={setShowDebug}
          LANGGRAPH_ASSISTANT_ID={LANGGRAPH_ASSISTANT_ID}
          createNewThread={createNewThread}
          onThreadId={onThreadId}
          fetchThreads={fetchThreads}
          deleteThread={deleteThread}
          deleteAllThreads={deleteAllThreads}
          timeAgoOnlyIfOver={timeAgoOnlyIfOver}
        />*/}
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={createNewThread}>
            <Plus className="h-4 w-4 mr-2" /> Create new thread
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                {threadId ? `Thread: ${threadId.slice(0, 8)}…` : "New thread"}
                <ChevronsUpDown className="h-4 w-4 opacity-60" />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" className="w-72">
              <DropdownMenuLabel className="flex items-center justify-between">
                Threads
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={fetchThreads}
                  disabled={loadingThreads}
                  className="h-6 w-6"
                >
                  <RefreshCcw
                    className={cn("h-4 w-4", loadingThreads && "animate-spin")}
                  />
                </Button>
              </DropdownMenuLabel>

              <DropdownMenuItem onClick={createNewThread}>
                <Plus className="h-4 w-4 mr-2" /> Create new thread
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {threadsList.length === 0 ? (
                <div className="px-2 py-1.5 text-xs text-muted-foreground">
                  No threads found
                </div>
              ) : (
                threadsList.map((t) => {
                  const lr = lastRunByThread[t.thread_id];
                  const assistant = lr?.assistant_id ?? LANGGRAPH_ASSISTANT_ID; // fallback to your active assistant
                  const when =
                    timeAgoOnlyIfOver(
                      t.updated_at ?? t.created_at ?? lr?.created_at
                    ) ||
                    new Date(
                      (t.updated_at ?? t.created_at ?? lr?.created_at) || ""
                    ).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    });

                  return (
                    <DropdownMenuItem
                      key={t.thread_id}
                      onClick={() => onThreadId(t.thread_id)}
                      className={cn(
                        "flex flex-col items-start gap-0.5",
                        t.thread_id === threadId && "bg-muted"
                      )}
                    >
                      <span className="font-medium">{t.thread_id}</span>
                      <div className="flex items-center gap-2 text-[11px] text-muted-foreground">
                        <Badge variant="secondary" className="h-5 px-2">
                          {assistant}
                        </Badge>
                        {t.status ? (
                          <span className="inline-flex items-center gap-1">
                            <span
                              className={cn(
                                "inline-block h-1.5 w-1.5 rounded-full",
                                t.status === "idle"
                                  ? "bg-green-500"
                                  : t.status === "busy"
                                  ? "bg-yellow-500"
                                  : t.status === "interrupted"
                                  ? "bg-orange-500"
                                  : "bg-red-500"
                              )}
                            />
                            {t.status}
                          </span>
                        ) : null}
                        {when ? <span>• {when}</span> : null}
                      </div>
                    </DropdownMenuItem>
                  );
                })
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          {/* NEW: Debug toggle */}
          <div className="flex items-center gap-2 pl-2 border-l">
            <Label htmlFor="debug" className="text-xs">
              Debug
            </Label>
            <Switch
              id="debug"
              checked={showDebug}
              onCheckedChange={setShowDebug}
            />
          </div>
          <Button variant="destructive" size="sm" onClick={deleteThread}>
            <DeleteIcon className="h-4 w-4 mr-2" /> Delete selected thread
          </Button>
          <Button variant="destructive" size="sm" onClick={deleteAllThreads}>
            <Trash2Icon className="h-4 w-4 mr-2" /> Delete All Threads
          </Button>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {thread.messages.map((m) => {
            // 1) Tool results as cards (matched to the AI call by tool_call_id)
            if (m.type === "tool") {
              if (!showDebug) return null; // NEW guard
              const mm: any = m;
              const toolCallId = mm.tool_call_id ?? mm.id;
              const prevAi = [...thread.messages]
                .reverse()
                .find(
                  (x) =>
                    x.type === "ai" &&
                    getToolCalls(x).some((tc) => tc.id === toolCallId)
                );
              const tc = prevAi
                ? getToolCalls(prevAi).find((tc) => tc.id === toolCallId)
                : undefined;

              const parsed =
                typeof mm.content === "string"
                  ? safeParseJSON(mm.content) ?? mm.content
                  : mm.content;

              return (
                <div key={m.id} className="px-4">
                  <ToolResultCard name={mm.name ?? tc?.name} result={parsed} />
                </div>
              );
            }

            // 2) Human/AI bubbles, with AI tool-call cards shown under the bubble
            const isUser = m.type === "human";
            const calls = getToolCalls(m as any);

            return (
              <div key={m.id} className="px-4 space-y-2">
                <ChatBubble message={m} isUser={isUser} />
                {!isUser && showDebug && calls.length > 0 && (
                  <div className="flex flex-col">
                    {calls.map((c, i) => (
                      <ToolCallCard key={c.id ?? i} call={c} />
                    ))}
                  </div>
                )}
              </div>
            );
          })}

          {/* streaming indicator */}
          {thread.isLoading && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Thinking…
            </div>
          )}
          {thread.isLoading && showDebug && toolEvents.length > 0 && (
            <div className="text-[11px] text-muted-foreground space-y-1">
              {toolEvents.slice(-8).map((e, i) => (
                <div key={i} className="flex items-center gap-2">
                  <span className="inline-block h-1.5 w-1.5 rounded-full bg-muted-foreground/60" />
                  <span>
                    {e.event ?? "event"}
                    {e.tool ? `: ${e.tool}` : ""}
                    {e.id ? ` (${String(e.id).slice(0, 6)}…)` : ""}
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* error bar */}
          {thread.error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2">
              {String(thread.error)}
            </div>
          )}

          <div ref={bottomRef} />
        </div>
      </ScrollArea>

      <Separator />

      {/* Composer */}
      <div className="p-2 md:p-3">
        <MessageInput
          onSend={sendText}
          placeholder="Write a message"
          showAttach
          onAttachClick={onAttachClick}
          suggestions={suggestions}
          suggestionMode="send"
          showSuggestionsWhen="empty"
          showLabel
          label={threadId ? `Thread: ${threadId}` : "New thread"}
        />
        <div className="flex items-center justify-between mt-2">
          <input
            ref={fileRef}
            type="file"
            accept="image/*"
            className="hidden"
            onChange={onFilePicked}
          />
          <div className="text-xs text-muted-foreground">
            Press <kbd className="border rounded px-1">Enter</kbd> to send,
            <span className="mx-1" />{" "}
            <kbd className="border rounded px-1">Shift</kbd>+
            <kbd className="border rounded px-1">Enter</kbd> for newline
          </div>

          {thread.isLoading ? (
            <Button variant="secondary" size="sm" onClick={() => thread.stop()}>
              Stop
            </Button>
          ) : null}
        </div>
      </div>
    </div>
  );
}

export default function Chat() {
  const [threadId, setThreadId] = useState<string | undefined>(undefined);
  // Force a full re-initialization of the stream when thread changes
  return (
    <ChatInner
      key={threadId ?? "__new__"}
      threadId={threadId}
      onThreadId={setThreadId}
    />
  );
}
