import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import "https://deno.land/x/xhr@0.1.0/mod.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // Get auth token from request
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { campaign_id, task, context } = await req.json();

    if (!campaign_id || !task) {
      return new Response(
        JSON.stringify({ error: 'campaign_id and task are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Marketing strategy agent processing task:', task, 'for campaign:', campaign_id);

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('*')
      .eq('id', campaign_id)
      .eq('user_id', user.id)
      .single();

    if (campaignError || !campaign) {
      return new Response(
        JSON.stringify({ error: 'Campaign not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Log the conversation
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'marketing_strategy',
        role: 'user',
        message: task,
        context_data: context || {}
      });

    let systemPrompt = '';
    let userPrompt = '';

    // Define different tasks for the marketing strategy agent
    switch (task) {
      case 'analyze_target_audience':
        systemPrompt = `You are a marketing strategy expert specializing in target audience analysis. Analyze the provided campaign information and provide detailed insights about the target audience, including demographics, psychographics, pain points, and messaging preferences.`;
        userPrompt = `Campaign: ${campaign.name}\nType: ${campaign.campaign_type}\nData: ${JSON.stringify(campaign.campaign_data, null, 2)}\n\nProvide a comprehensive target audience analysis.`;
        break;

      case 'create_marketing_strategy':
        systemPrompt = `You are a senior marketing strategist. Create a comprehensive marketing strategy based on the campaign information provided. Include objectives, key messages, channel recommendations, timeline, and success metrics.`;
        userPrompt = `Campaign: ${campaign.name}\nType: ${campaign.campaign_type}\nData: ${JSON.stringify(campaign.campaign_data, null, 2)}\n\nCreate a detailed marketing strategy.`;
        break;

      case 'generate_content_ideas':
        systemPrompt = `You are a creative marketing content strategist. Generate innovative and engaging content ideas for the marketing campaign. Include various content formats, themes, and distribution strategies.`;
        userPrompt = `Campaign: ${campaign.name}\nType: ${campaign.campaign_type}\nData: ${JSON.stringify(campaign.campaign_data, null, 2)}\n\nGenerate creative content ideas for this campaign.`;
        break;

      case 'competitive_analysis':
        systemPrompt = `You are a competitive intelligence analyst. Provide insights on competitive landscape, market positioning, and differentiation opportunities based on the campaign information.`;
        userPrompt = `Campaign: ${campaign.name}\nType: ${campaign.campaign_type}\nData: ${JSON.stringify(campaign.campaign_data, null, 2)}\n\nProvide competitive analysis and positioning recommendations.`;
        break;

      default:
        systemPrompt = `You are a comprehensive marketing strategy consultant. Analyze the provided campaign information and provide strategic recommendations based on the specific task requested.`;
        userPrompt = `Campaign: ${campaign.name}\nType: ${campaign.campaign_type}\nData: ${JSON.stringify(campaign.campaign_data, null, 2)}\n\nTask: ${task}\n\nContext: ${JSON.stringify(context || {}, null, 2)}`;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0]?.message?.content?.trim();

    if (!aiResponse) {
      throw new Error('Failed to generate marketing strategy response');
    }

    // Log the AI response
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'marketing_strategy',
        role: 'assistant',
        message: aiResponse,
        context_data: { task, model_used: 'gpt-4o-mini' }
      });

    // Update campaign with the current agent
    await supabase
      .from('marketing_campaigns')
      .update({
        current_agent: 'marketing_strategy',
        updated_at: new Date().toISOString()
      })
      .eq('id', campaign_id);

    console.log('Marketing strategy agent completed task successfully');

    return new Response(JSON.stringify({
      success: true,
      response: aiResponse,
      task,
      campaign_id,
      agent_type: 'marketing_strategy'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in marketing-strategy-agent function:', error);
    
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to process marketing strategy task'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});