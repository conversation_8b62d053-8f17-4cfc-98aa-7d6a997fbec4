import React, { useState, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Video, X, CheckCircle2, Upload, ArrowLeftRight } from 'lucide-react';

type JobRow = {
  id: string;
  status: 'queued'|'running'|'done'|'error';
  progress: number;
  result: any | null;
  error: string | null;
  message: string | null;
};

interface UCGVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string | null;
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onStartCreation: (  entity_name: string, entity_id: string, prompt: string, mergedImageBlob?: Blob) => void;
  job: JobRow | null;
  entity_name: string;
  entity_id: string;
}

const UCGVideoModal: React.FC<UCGVideoModalProps> = ({
  isOpen,
  onClose,
  imageUrl,
  prompt,
  onPromptChange,
  onStartCreation,
  entity_name,
  entity_id,
  job,
}) => {
  const [secondImageUrl, setSecondImageUrl] = useState<string | null>(null);
  const [secondImageFile, setSecondImageFile] = useState<File | null>(null);
  const [mergedImageUrl, setMergedImageUrl] = useState<string | null>(null);
  const [mergedImageBlob, setMergedImageBlob] = useState<Blob | null>(null);
  const [isFlipped, setIsFlipped] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const isProcessing = job?.status === 'queued' || job?.status === 'running';
  const isCompleted = job?.status === 'done';
  const hasError = job?.status === 'error';

  // Function to merge two images horizontally
  const mergeImages = useCallback(async (img1Url: string, img2Url: string, flip: boolean) => {
    return new Promise<{url: string, blob: Blob}>((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      const img1 = new Image();
      const img2 = new Image();
      let loadedImages = 0;

      const onImageLoad = () => {
        loadedImages++;
        if (loadedImages === 2) {
          try {
            // Calculate dimensions - make both images the same height
            const maxHeight = Math.max(img1.height, img2.height);
            const img1Width = (img1.width * maxHeight) / img1.height;
            const img2Width = (img2.width * maxHeight) / img2.height;
            
            canvas.width = img1Width + img2Width;
            canvas.height = maxHeight;

            // Determine order based on flip state
            const firstImg = flip ? img2 : img1;
            const secondImg = flip ? img1 : img2;
            const firstWidth = flip ? img2Width : img1Width;
            const secondWidth = flip ? img1Width : img2Width;

            // Draw images side by side
            ctx.drawImage(firstImg, 0, 0, firstWidth, maxHeight);
            ctx.drawImage(secondImg, firstWidth, 0, secondWidth, maxHeight);

            // Convert to blob
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                resolve({ url, blob });
              } else {
                reject(new Error('Failed to create blob'));
              }
            }, 'image/jpeg', 0.8);
          } catch (error) {
            reject(new Error(`Canvas operation failed: ${error}`));
          }
        }
      };

      const onImageError = (imageIndex: number) => (error: any) => {
        console.error(`Failed to load image ${imageIndex}:`, error);
        reject(new Error(`Failed to load image ${imageIndex}`));
      };

      // Set crossOrigin to handle CORS issues
      img1.crossOrigin = 'anonymous';
      img2.crossOrigin = 'anonymous';
      
      img1.onload = onImageLoad;
      img2.onload = onImageLoad;
      img1.onerror = onImageError(1);
      img2.onerror = onImageError(2);
      
      // Add timeout to prevent hanging
      const timeout = setTimeout(() => {
        reject(new Error('Image loading timeout'));
      }, 10000);

      const originalResolve = resolve;
      const originalReject = reject;
      
      resolve = (value) => {
        clearTimeout(timeout);
        originalResolve(value);
      };
      
      reject = (reason) => {
        clearTimeout(timeout);
        originalReject(reason);
      };
      
      img1.src = img1Url;
      img2.src = img2Url;
    });
  }, []);

  // Handle second image upload
  const handleSecondImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSecondImageFile(file);
      const url = URL.createObjectURL(file);
      setSecondImageUrl(url);
    }
  }, []);

  // Merge images when both are available
  React.useEffect(() => {
    if (imageUrl && secondImageUrl) {
      mergeImages(imageUrl, secondImageUrl, isFlipped)
        .then(({ url, blob }) => {
          setMergedImageUrl(url);
          setMergedImageBlob(blob);
        })
        .catch(console.error);
    } else {
      setMergedImageUrl(null);
      setMergedImageBlob(null);
    }
  }, [imageUrl, secondImageUrl, isFlipped, mergeImages]);

  // Handle video creation
  const handleCreateVideo = useCallback(() => {
    if (mergedImageBlob) {
      onStartCreation(entity_name, entity_id, prompt, mergedImageBlob);
    } else {
      onStartCreation( entity_name, entity_id, prompt);
    }
  }, [prompt, mergedImageBlob, onStartCreation]);
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            UCG Video Creation
          </DialogTitle>
         
        </DialogHeader>

        <div className="space-y-6">
          {/* Prompt Section - Moved to top */}
          <div>
            <h3 className="font-medium mb-2">Video Prompt:</h3>
            <Textarea
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              placeholder="Describe what kind of video you want to create..."
              className="min-h-[80px]"
              disabled={isProcessing}
            />
          </div>

          {/* Control Buttons - In the middle */}
          {secondImageUrl && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFlipped(!isFlipped)}
                disabled={isProcessing}
              >
                <ArrowLeftRight className="h-4 w-4 mr-2" />
                Swap Positions
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSecondImageUrl(null);
                  setSecondImageFile(null);
                  setIsFlipped(false);
                }}
                disabled={isProcessing}
              >
                <X className="h-4 w-4 mr-2" />
                Remove Second Image
              </Button>
            </div>
          )}

          {/* Merged Images Section - Always visible */}
          {imageUrl && (
            <div>
              <h3 className="font-medium mb-2">
                {mergedImageUrl ? "Merged Images" : "Source Images"}:
              </h3>
              
              <div className="space-y-3">
                {mergedImageUrl ? (
                  /* Show only merged image when available */
                  <div className="flex justify-center">
                    <div className="relative max-w-2xl">
                      <img
                        src={mergedImageUrl}
                        alt="Merged images for video"
                        className="w-full h-[200px] object-cover rounded-lg border shadow-sm"
                      />
                      <div className="absolute top-2 right-2 bg-primary/90 text-primary-foreground text-xs px-2 py-1 rounded">
                        Merged
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Show individual images or placeholder when no merge */
                  <div className="grid grid-cols-2 gap-4 max-w-4xl mx-auto">
                    {/* First image */}
                    <div className="relative">
                      <img
                        src={imageUrl}
                        alt="Source image for video"
                        className="w-full h-[200px] object-cover rounded-lg border shadow-sm"
                      />
                    </div>
                    
                    {/* Second image area */}
                    <div className="relative">
                      {secondImageUrl ? (
                        <div className="relative w-full">
                          <img
                            src={secondImageUrl}
                            alt="Second image for video merging"
                            className="w-full h-[200px] object-cover rounded-lg border shadow-sm"
                          />
                        </div>
                      ) : (
                        /* Show placeholder when no second image */
                        <div className="h-[200px] border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-center p-4 hover:border-gray-400 transition-colors cursor-pointer"
                             onClick={() => fileInputRef.current?.click()}>
                          <Upload className="h-8 w-8 mb-2 text-gray-400" />
                          <p className="text-lg font-semibold text-blue-700">
                            Upload Second Image
                          </p>
                          <p className="text-sm text-gray-500 mt-1">
                            (optional)
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Show merging status */}
                {secondImageUrl && !mergedImageUrl && (
                  <div className="flex justify-center">
                    <div className="text-center">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">Merging images...</p>
                    </div>
                  </div>
                )}

                {/* Second Image Upload Area - Always visible at bottom */}
                <div className="flex flex-col gap-2">
                  <Label htmlFor="second-image" className="cursor-pointer">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                      <Upload className="h-6 w-6 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-600">
                        {secondImageUrl ? "Change second image" : "Add second image (optional)"}
                      </p>
                    </div>
                  </Label>
                  <Input
                    id="second-image"
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleSecondImageUpload}
                    className="hidden"
                    disabled={isProcessing}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Create Button */}
          {!job && (
            <div className="flex justify-center">
              <Button 
                onClick={handleCreateVideo}
                className="w-full max-w-xs flex items-center gap-2"
                disabled={!prompt.trim()}
              >
                <Video className="h-4 w-4" />
                Create Video {mergedImageUrl ? '(Merged Images)' : ''}
              </Button>
            </div>
          )}

          {/* Status Section */}
          <div className="text-center space-y-4">
            {isProcessing && (
              <>
                <div className="flex justify-center">
                  <div className="relative">
                    <Loader2 className="h-16 w-16 animate-spin text-primary" />
                    <Video className="h-8 w-8 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-primary" />
                  </div>
                </div>
                <div className="space-y-4">
                  <p className="font-medium text-lg">
                    {job?.status === 'queued' ? 'Queued for processing...' : (job?.message || 'Creating your video...')}
                  </p>
                  <p className="text-muted-foreground">
                    It is going to take about 10 minutes. Do not close the app neither refresh the page.
                  </p>
                  
                  {/* Progress Bar */}
                  <div className="w-full max-w-md mx-auto space-y-2">
                    <Progress value={job?.progress || 0} className="w-full" />
                    <p className="text-sm text-muted-foreground">
                      {job?.progress || 0}% complete
                    </p>
                  </div>
                  
                  <div className="flex justify-center">
                    <div className="flex space-x-1">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="w-2 h-2 bg-primary rounded-full animate-pulse"
                          style={{
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: '1s',
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </>
            )}

            {isCompleted && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle2 className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <h3 className="font-medium text-lg text-green-600">Video Created Successfully!</h3>
                
                {job?.result?.data && (
                  <div className="space-y-4">
                    <div className="bg-background border rounded-lg overflow-hidden">
                      <video
                        src={job.result.data}
                        controls
                        className="w-full max-h-96"
                        preload="metadata"
                      >
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    
                    <div className="flex gap-2 justify-center">
                      <Button 
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = job.result.data;
                          link.download = `ucg-video-${Date.now()}.mp4`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Video className="h-4 w-4" />
                        Download Video
                      </Button>
                      
                      <Button 
                        onClick={() => window.open(job.result.data, '_blank')}
                        variant="outline"
                      >
                        Open in New Tab
                      </Button>
                    </div>
                  </div>
                )}
                
                {!job?.result?.data && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <p className="text-sm text-green-800">
                      Video generation completed, but no video URL was provided.
                    </p>
                  </div>
                )}
              </div>
            )}

            {hasError && (
              <div className="space-y-3">
                <div className="flex justify-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                    <X className="h-8 w-8 text-red-600" />
                  </div>
                </div>
                <h3 className="font-medium text-lg text-red-600">Video Creation Failed</h3>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-800">{job?.error || 'An unknown error occurred'}</p>
                </div>
              </div>
            )}
          </div>

          {/* Close Button */}
          {(isCompleted || hasError) && (
            <div className="flex justify-center">
              <Button onClick={onClose} className="w-full max-w-xs">
                Close
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UCGVideoModal;