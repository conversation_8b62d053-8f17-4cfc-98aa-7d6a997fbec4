import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import ProjectHeader from '@/components/project/ProjectHeader';
import AssetsPanel from '@/components/project/AssetsPanel';
import CanvasWorkspace from '@/components/project/CanvasWorkspace';
import PropertiesPanel from '@/components/project/PropertiesPanel';
import MobileProjectTabs from '@/components/project/MobileProjectTabs';
import { useProject } from '@/hooks/useProject';

const ProjectPage = () => {
  const { id } = useParams<{ id: string }>();
  const isMobile = useIsMobile();
  const { data: project, isLoading } = useProject(id || '');
  
  const [selectedElement, setSelectedElement] = useState<string | null>(null);

  if (isLoading) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-muted-foreground">Loading project...</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-muted-foreground">Project not found</div>
      </div>
    );
  }

  const handleElementSelect = (elementId: string | null) => {
    setSelectedElement(elementId);
  };

  const handleElementClose = () => {
    setSelectedElement(null);
  };

  return (
    <div className="h-screen bg-background flex flex-col overflow-hidden">
      <ProjectHeader
        title={project.title}
        projectId={project.id}
        formatPreset={project.format_preset}
      />
      
      {!isMobile ? (
        <div className="flex-1 flex gap-4 p-4 pr-12 overflow-hidden">
          <div className="w-80 flex-shrink-0">
            <AssetsPanel
              projectId={project.id}
              onElementAdd={handleElementSelect}
            />
          </div>
          
          <div className={`flex-1 ${selectedElement ? 'mr-80' : ''} transition-all duration-300`}>
            <CanvasWorkspace
              project={project}
              selectedElement={selectedElement}
              onElementSelect={handleElementSelect}
            />
          </div>
          
          {selectedElement && (
            <div className="w-80 flex-shrink-0">
              <PropertiesPanel
                projectId={project.id}
                selectedElement={selectedElement}
                onClose={handleElementClose}
              />
            </div>
          )}
        </div>
      ) : (
        <div className="flex-1 overflow-hidden">
          <MobileProjectTabs
            project={project}
            selectedElement={selectedElement}
            onElementSelect={handleElementSelect}
            onElementClose={handleElementClose}
          />
        </div>
      )}
    </div>
  );
};

export default ProjectPage;