import { useState, useEffect } from 'react';
import { useN8nJob } from './useN8nJob';
import { useProjectJobs } from './useProjectJobs';

export const useUCGVideo = (projectId: string | null) => {
  const { job, startJob } = useN8nJob();
  const { activeJobs } = useProjectJobs(projectId);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentImageUrl, setCurrentImageUrl] = useState<string | null>(null);
  const [currentPrompt, setCurrentPrompt] = useState('create a short UCG video in Italian language and without subtitle');
  const [existingJob, setExistingJob] = useState<any>(null);

  // Check for existing UCG video jobs when modal opens
  useEffect(() => {
    if (isModalOpen && activeJobs.length > 0) {
      // Look for UCG video jobs (you may need to adjust the message filter based on your job structure)
      const ucgJob = activeJobs.find(job => 
        job.message?.toLowerCase().includes('ucg video') || 
        job.message?.toLowerCase().includes('video')
      );
      
      if (ucgJob) {
        setExistingJob(ucgJob);
      }
    }
  }, [isModalOpen, activeJobs]);

  const openModal = (imageUrl: string) => {
    setCurrentImageUrl(imageUrl);
    setIsModalOpen(true);
  };

  const startUCGVideoCreation = async (entity_name: string, entity_id: string, prompt: string, mergedImageBlob?: Blob) => {
    if (!currentImageUrl && !mergedImageBlob) return;
    
    try {
      let imageBlob: Blob;
      
      if (mergedImageBlob) {
        // Use the merged image blob if provided
        imageBlob = mergedImageBlob;
      } else {
        // Fetch the original image to convert it to a blob
        const imageResponse = await fetch(currentImageUrl!);
        if (!imageResponse.ok) {
          throw new Error('Failed to fetch image');
        }
        imageBlob = await imageResponse.blob();
      }
      
      // Create form data for the N8n job
      const formData = new FormData();
      formData.append('message', prompt);
      formData.append('image', imageBlob, mergedImageBlob ? 'merged-image.jpg' : 'project-image.jpg');
      formData.append('uuid', crypto.randomUUID());
      formData.append("entity_name", entity_name);
      formData.append("entity_id", entity_id);

      // Start the job using the N8n job manager
      await startJob(formData);
    } catch (error) {
      console.error('Failed to start UCG video creation:', error);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentImageUrl(null);
    setExistingJob(null);
  };

  return {
    openModal,
    startUCGVideoCreation,
    // Modal state
    isModalOpen,
    closeModal,
    currentImageUrl,
    // Prompt state
    currentPrompt,
    setCurrentPrompt,
    // Job state
    job: existingJob || job, // Use existing job if available, otherwise use new job
    existingJob,
  };
};