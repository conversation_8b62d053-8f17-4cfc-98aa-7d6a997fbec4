import React, { useState } from "react";
import { Clock, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useNotebookDelete } from "@/hooks/useNotebookDelete";
import { useCampaigns } from "@/hooks/useCampaigns";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { formatDate } from "@/utils/date";
import { Image } from 'lucide-react';
interface NotebookCardProps {
  project: {
    id: string;
    title: string;
    date: string;
    image_url: string;
    updated_at: string,

    // sources: number;
    // icon: string;
    // color: string;
    // hasCollaborators?: boolean;
  };
}

const CampaignCard = ({ project }: NotebookCardProps) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { deleteProject, isDeleting } = useCampaigns();

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log("Delete button clicked for notebook:", project.id);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log("Confirming delete for project:", project.id);
    deleteProject(project.id);
    setShowDeleteDialog(false);
  };

  // Generate CSS classes from color name
  const colorName = "gray"; //project.color ||
  const backgroundClass = `bg-${colorName}-100`;
  const borderClass = `border-${colorName}-200`;

  return (
    <div
      className={`rounded-lg border ${borderClass} ${backgroundClass} p-4 hover:shadow-md transition-shadow cursor-pointer relative  flex flex-col`}
    >
     

    

      <Card
        key={project.id}
        className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 group relative"
      >
        {/* Delete Button */}
        <div className="absolute bottom-3 right-3" data-delete-action="true">
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogTrigger asChild>
            <button
              onClick={handleDeleteClick}
              className="p-1 hover:bg-red-50 rounded text-gray-400 hover:text-red-500 transition-colors delete-button"
              disabled={isDeleting}
              data-delete-action="true"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete this notebook?</AlertDialogTitle>
              <AlertDialogDescription>
                You're about to delete this notebook and all of its content.
                This cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmDelete}
                className="bg-blue-600 hover:bg-blue-700"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
</div>
        <CardHeader className="pb-2">
          <div className="aspect-video w-full bg-muted rounded-md overflow-hidden mb-3">
            {project.image_url ? (
              <img
                src={project.image_url}
                alt={project.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Image className="h-8 w-8 text-muted-foreground" />
              </div>
            )}
          </div>
          <CardTitle className="text-lg truncate">{project.title}</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {formatDate(project.updated_at)}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignCard;
