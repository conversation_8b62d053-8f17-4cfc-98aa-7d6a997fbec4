import React from 'react';
import { FormatPreset } from '../constants/formatPresets';
import {
  calculateOverlayDimensions,
  calculateGridLines,
  getSafeAreaMargins,
  CanvasInfo
} from '../utils/overlayCalculations';

interface CanvasOverlayProps {
  isVisible: boolean;
  selectedFormat: FormatPreset;
  canvasWidth: number;
  canvasHeight: number;
  canvasScale?: number;
  overlayMode?: 'crop' | 'highlight' | 'grid';
  className?: string;
  showSafeArea?: boolean;
}

/**
 * CanvasOverlay component that displays visual indicators for social media format crop areas
 */
export const CanvasOverlay: React.FC<CanvasOverlayProps> = ({
  isVisible,
  selectedFormat,
  canvasWidth,
  canvasHeight,
  canvasScale = 1,
  overlayMode = 'crop',
  className = '',
  showSafeArea = false
}) => {
  if (!isVisible) {
    return null;
  }

  const canvasInfo: CanvasInfo = {
    width: canvasWidth,
    height: canvasHeight,
    scale: canvasScale
  };

  const dimensions = calculateOverlayDimensions(canvasInfo, selectedFormat);
  const safeAreaMargins = getSafeAreaMargins(selectedFormat);
  const gridLines = calculateGridLines(
    dimensions.cropWidth,
    dimensions.cropHeight,
    dimensions.cropOffsetX,
    dimensions.cropOffsetY
  );

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: `${canvasWidth}px`,
    height: `${canvasHeight}px`,
    pointerEvents: 'none',
    zIndex: 10,
    transform: `scale(${canvasScale})`,
    transformOrigin: 'top left',
    opacity: isVisible ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out'
  };

  const cropAreaStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${dimensions.cropOffsetX}px`,
    top: `${dimensions.cropOffsetY}px`,
    width: `${dimensions.cropWidth}px`,
    height: `${dimensions.cropHeight}px`,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
  };

  const renderCropOverlay = () => (
    <div style={overlayStyle} className={`canvas-overlay ${className}`}>
      {/* Dimmed areas outside crop */}
      {dimensions.cropOffsetY > 0 && (
        <>
          {/* Top area */}
          <div
            className="absolute bg-black/30"
            style={{
              top: 0,
              left: 0,
              width: '100%',
              height: `${dimensions.cropOffsetY}px`
            }}
          />
          {/* Bottom area */}
          <div
            className="absolute bg-black/30"
            style={{
              bottom: 0,
              left: 0,
              width: '100%',
              height: `${dimensions.cropOffsetY}px`
            }}
          />
        </>
      )}

      {dimensions.cropOffsetX > 0 && (
        <>
          {/* Left area */}
          <div
            className="absolute bg-black/30"
            style={{
              top: 0,
              left: 0,
              width: `${dimensions.cropOffsetX}px`,
              height: '100%'
            }}
          />
          {/* Right area */}
          <div
            className="absolute bg-black/30"
            style={{
              top: 0,
              right: 0,
              width: `${dimensions.cropOffsetX}px`,
              height: '100%'
            }}
          />
        </>
      )}
      
      {/* Crop area border */}
      <div
        style={cropAreaStyle}
        className="border-2 border-primary border-dashed animate-pulse"
      >
        {/* Corner indicators with enhanced styling */}
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-white" />
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-white" />
        <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-white" />
        <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-primary rounded-full shadow-lg border-2 border-white" />

        {/* Edge handles */}
        <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-6 h-2 bg-primary rounded-full opacity-75" />
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-2 bg-primary rounded-full opacity-75" />
        <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-6 bg-primary rounded-full opacity-75" />
        <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-6 bg-primary rounded-full opacity-75" />

        {/* Format label with enhanced styling */}
        <div className="absolute -top-10 left-0 bg-primary text-primary-foreground px-3 py-1.5 rounded-lg text-xs font-medium whitespace-nowrap shadow-lg border border-primary-foreground/20">
          <div className="flex items-center gap-2">
            <span className="text-lg">{selectedFormat.icon}</span>
            <div>
              <div className="font-semibold">{selectedFormat.name}</div>
              <div className="text-xs opacity-90">{selectedFormat.width}×{selectedFormat.height}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderHighlightOverlay = () => (
    <div style={overlayStyle} className={`canvas-overlay ${className}`}>
      {/* Highlighted crop area */}
      <div
        style={cropAreaStyle}
        className="border-2 border-primary bg-gradient-to-br from-primary/20 via-primary/10 to-primary/5 backdrop-blur-sm rounded-lg shadow-lg"
      >
        {/* Subtle inner glow */}
        <div className="absolute inset-1 border border-primary/30 rounded-md" />

        {/* Format label with enhanced styling */}
        <div className="absolute -top-10 left-0 bg-gradient-to-r from-primary to-primary/90 text-primary-foreground px-3 py-1.5 rounded-lg text-xs font-medium whitespace-nowrap shadow-lg">
          <div className="flex items-center gap-2">
            <span className="text-lg">{selectedFormat.icon}</span>
            <div>
              <div className="font-semibold">{selectedFormat.name}</div>
              <div className="text-xs opacity-90">{selectedFormat.width}×{selectedFormat.height}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderGridOverlay = () => (
    <div style={overlayStyle} className={`canvas-overlay ${className}`}>
      {/* Grid lines */}
      <div
        style={cropAreaStyle}
        className="border-2 border-primary rounded-lg shadow-lg"
      >
        {/* Rule of thirds grid */}
        <div className="absolute inset-0 rounded-md overflow-hidden">
          {/* Vertical lines */}
          {gridLines.verticalLines.map((x, index) => (
            <div
              key={`v-${index}`}
              className="absolute top-0 bottom-0 w-px bg-gradient-to-b from-primary/60 via-primary/40 to-primary/60 shadow-sm"
              style={{ left: `${((x - dimensions.cropOffsetX) / dimensions.cropWidth) * 100}%` }}
            />
          ))}
          {/* Horizontal lines */}
          {gridLines.horizontalLines.map((y, index) => (
            <div
              key={`h-${index}`}
              className="absolute left-0 right-0 h-px bg-gradient-to-r from-primary/60 via-primary/40 to-primary/60 shadow-sm"
              style={{ top: `${((y - dimensions.cropOffsetY) / dimensions.cropHeight) * 100}%` }}
            />
          ))}

          {/* Grid intersection points */}
          {gridLines.verticalLines.map((x, vIndex) =>
            gridLines.horizontalLines.map((y, hIndex) => (
              <div
                key={`intersection-${vIndex}-${hIndex}`}
                className="absolute w-2 h-2 bg-primary rounded-full transform -translate-x-1/2 -translate-y-1/2 shadow-md"
                style={{
                  left: `${((x - dimensions.cropOffsetX) / dimensions.cropWidth) * 100}%`,
                  top: `${((y - dimensions.cropOffsetY) / dimensions.cropHeight) * 100}%`
                }}
              />
            ))
          )}
        </div>

        {/* Safe area indicators */}
        {showSafeArea && (
          <div className="absolute inset-0 border border-yellow-400 border-dashed opacity-60"
            style={{
              margin: `${(safeAreaMargins.top / selectedFormat.height) * dimensions.cropHeight}px ${(safeAreaMargins.right / selectedFormat.width) * dimensions.cropWidth}px ${(safeAreaMargins.bottom / selectedFormat.height) * dimensions.cropHeight}px ${(safeAreaMargins.left / selectedFormat.width) * dimensions.cropWidth}px`
            }}
          />
        )}

        {/* Format label with enhanced styling */}
        <div className="absolute -top-10 left-0 bg-primary text-primary-foreground px-3 py-1.5 rounded-lg text-xs font-medium whitespace-nowrap shadow-lg">
          <div className="flex items-center gap-2">
            <span className="text-lg">{selectedFormat.icon}</span>
            <div>
              <div className="font-semibold">{selectedFormat.name}</div>
              <div className="text-xs opacity-90">{selectedFormat.width}×{selectedFormat.height}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  switch (overlayMode) {
    case 'highlight':
      return renderHighlightOverlay();
    case 'grid':
      return renderGridOverlay();
    case 'crop':
    default:
      return renderCropOverlay();
  }
};

export default CanvasOverlay;
