import React from 'react';
import { FormatPreset } from '../constants/formatPresets';
import {
  calculateOverlayDimensions,
  CanvasInfo
} from '../utils/overlayCalculations';

interface CanvasOverlayProps {
  isVisible: boolean;
  selectedFormat: FormatPreset;
  canvasWidth: number;
  canvasHeight: number;
  canvasScale?: number;
  className?: string;
}

/**
 * CanvasOverlay component that displays a blur effect on areas outside the social media format crop boundaries
 */
export const CanvasOverlay: React.FC<CanvasOverlayProps> = ({
  isVisible,
  selectedFormat,
  canvasWidth,
  canvasHeight,
  canvasScale = 1,
  className = ''
}) => {
  if (!isVisible) {
    return null;
  }

  const canvasInfo: CanvasInfo = {
    width: canvasWidth,
    height: canvasHeight,
    scale: canvasScale
  };

  const dimensions = calculateOverlayDimensions(canvasInfo, selectedFormat);

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: `${canvasWidth}px`,
    height: `${canvasHeight}px`,
    pointerEvents: 'none',
    zIndex: 10,
    transform: `scale(${canvasScale})`,
    transformOrigin: 'top left',
    opacity: isVisible ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out'
  };

  const cropAreaStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${dimensions.cropOffsetX}px`,
    top: `${dimensions.cropOffsetY}px`,
    width: `${dimensions.cropWidth}px`,
    height: `${dimensions.cropHeight}px`,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
  };

  return (
    <div style={overlayStyle} className={`canvas-overlay ${className}`}>
      {/* Blur effect on areas outside crop boundaries */}

      {/* Top blurred area */}
      {dimensions.cropOffsetY > 0 && (
        <div
          className="absolute backdrop-blur-sm bg-black/10"
          style={{
            top: 0,
            left: 0,
            width: '100%',
            height: `${dimensions.cropOffsetY}px`,
            filter: 'blur(3px)'
          }}
        />
      )}

      {/* Bottom blurred area */}
      {dimensions.cropOffsetY > 0 && (
        <div
          className="absolute backdrop-blur-sm bg-black/10"
          style={{
            bottom: 0,
            left: 0,
            width: '100%',
            height: `${dimensions.cropOffsetY}px`,
            filter: 'blur(3px)'
          }}
        />
      )}

      {/* Left blurred area */}
      {dimensions.cropOffsetX > 0 && (
        <div
          className="absolute backdrop-blur-sm bg-black/10"
          style={{
            top: 0,
            left: 0,
            width: `${dimensions.cropOffsetX}px`,
            height: '100%',
            filter: 'blur(3px)'
          }}
        />
      )}

      {/* Right blurred area */}
      {dimensions.cropOffsetX > 0 && (
        <div
          className="absolute backdrop-blur-sm bg-black/10"
          style={{
            top: 0,
            right: 0,
            width: `${dimensions.cropOffsetX}px`,
            height: '100%',
            filter: 'blur(3px)'
          }}
        />
      )}

      {/* Clear crop area with subtle border */}
      <div
        style={cropAreaStyle}
        className="border-2 border-primary/60 border-dashed"
      >
        {/* Format label */}
        <div className="absolute -top-8 left-0 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium whitespace-nowrap shadow-lg">
          <div className="flex items-center gap-1.5">
            <span>{selectedFormat.icon}</span>
            <span>{selectedFormat.name}</span>
            <span className="opacity-75">({selectedFormat.width}×{selectedFormat.height})</span>
          </div>
        </div>
      </div>
    </div>
  );

};

export default CanvasOverlay;
