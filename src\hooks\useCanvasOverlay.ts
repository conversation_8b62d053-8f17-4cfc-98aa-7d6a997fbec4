import { useState, useCallback } from 'react';
import { FormatPreset } from '../constants/formatPresets';

export type OverlayMode = 'crop' | 'highlight' | 'grid';

interface CanvasOverlayState {
  isVisible: boolean;
  mode: OverlayMode;
  selectedFormat: FormatPreset | null;
}

interface CanvasOverlayActions {
  toggleOverlay: () => void;
  setOverlayVisible: (visible: boolean) => void;
  setOverlayMode: (mode: OverlayMode) => void;
  setSelectedFormat: (format: FormatPreset | null) => void;
  resetOverlay: () => void;
}

interface UseCanvasOverlayReturn extends CanvasOverlayState, CanvasOverlayActions {}

/**
 * Custom hook for managing canvas overlay state
 */
export const useCanvasOverlay = (
  initialFormat?: FormatPreset | null,
  initialVisible: boolean = false,
  initialMode: OverlayMode = 'crop'
): UseCanvasOverlayReturn => {
  const [isVisible, setIsVisible] = useState<boolean>(initialVisible);
  const [mode, setMode] = useState<OverlayMode>(initialMode);
  const [selectedFormat, setSelectedFormat] = useState<FormatPreset | null>(
    initialFormat || null
  );

  const toggleOverlay = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  const setOverlayVisible = useCallback((visible: boolean) => {
    setIsVisible(visible);
  }, []);

  const setOverlayMode = useCallback((newMode: OverlayMode) => {
    setMode(newMode);
  }, []);

  const setSelectedFormatCallback = useCallback((format: FormatPreset | null) => {
    setSelectedFormat(format);
  }, []);

  const resetOverlay = useCallback(() => {
    setIsVisible(false);
    setMode('crop');
    setSelectedFormat(null);
  }, []);

  return {
    isVisible,
    mode,
    selectedFormat,
    toggleOverlay,
    setOverlayVisible,
    setOverlayMode,
    setSelectedFormat: setSelectedFormatCallback,
    resetOverlay
  };
};

export default useCanvasOverlay;
