import { useState, useCallback } from 'react';
import { FormatPreset } from '../constants/formatPresets';

interface CanvasOverlayState {
  isVisible: boolean;
  selectedFormat: FormatPreset | null;
}

interface CanvasOverlayActions {
  toggleOverlay: () => void;
  setOverlayVisible: (visible: boolean) => void;
  setSelectedFormat: (format: FormatPreset | null) => void;
  resetOverlay: () => void;
}

interface UseCanvasOverlayReturn extends CanvasOverlayState, CanvasOverlayActions {}

/**
 * Custom hook for managing canvas overlay state
 */
export const useCanvasOverlay = (
  initialFormat?: FormatPreset | null,
  initialVisible: boolean = false
): UseCanvasOverlayReturn => {
  const [isVisible, setIsVisible] = useState<boolean>(initialVisible);
  const [selectedFormat, setSelectedFormat] = useState<FormatPreset | null>(
    initialFormat || null
  );

  const toggleOverlay = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  const setOverlayVisible = useCallback((visible: boolean) => {
    setIsVisible(visible);
  }, []);

  const setSelectedFormatCallback = useCallback((format: FormatPreset | null) => {
    setSelectedFormat(format);
  }, []);

  const resetOverlay = useCallback(() => {
    setIsVisible(false);
    setSelectedFormat(null);
  }, []);

  return {
    isVisible,
    selectedFormat,
    toggleOverlay,
    setOverlayVisible,
    setSelectedFormat: setSelectedFormatCallback,
    resetOverlay
  };
};

export default useCanvasOverlay;
