import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { <PERSON>c<PERSON>, Mic, Send, Loader2, <PERSON>rkles } from "lucide-react";

export type Suggestion = { id?: string; text: string };

export type MessageInputProps = {
  onSend: (text: string) => void | Promise<void>;
  value?: string; // controlled (optional)
  onChange?: (text: string) => void;
  placeholder?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  maxRows?: number; // default 6
  minRows?: number; // default 1
  className?: string;
  ariaLabel?: string;
  showAttach?: boolean; // default true
  showMic?: boolean; // default false
  onAttachClick?: () => void;
  onMicClick?: () => void;

  // Label row (right side, small text)
  showLabel?: boolean;
  label?: string;

  // NEW: Suggested questions
  suggestions?: Suggestion[];                 // e.g., [{text: "What are today’s deals?"}]
  suggestionMode?: "insert" | "send";         // default "insert"
  showSuggestionsWhen?: "empty" | "focused" | "always" | "never"; // default "empty"
  onSuggestionSelect?: (s: Suggestion) => void;
};

export const MessageInput: React.FC<MessageInputProps> = ({
  onSend,
  value,
  onChange,
  placeholder = "Write a message",
  disabled = false,
  autoFocus = false,
  maxRows = 6,
  minRows = 1,
  className = "",
  ariaLabel,
  showAttach = true,
  showMic = false,
  onAttachClick,
  onMicClick,
  showLabel = false,
  label = "Label",
  suggestions = [],
  suggestionMode = "insert",
  showSuggestionsWhen = "empty",
  onSuggestionSelect,
}) => {
  const isControlled = typeof value === "string";
  const [inner, setInner] = React.useState<string>(value ?? "");
  const [sending, setSending] = React.useState(false);
  const [focused, setFocused] = React.useState(false);
  const taRef = React.useRef<HTMLTextAreaElement | null>(null);
  const composingRef = React.useRef(false);

  // sync for controlled usage
  React.useEffect(() => {
    if (isControlled) setInner(value!);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const setText = React.useCallback(
    (txt: string) => {
      if (isControlled) onChange?.(txt);
      else setInner(txt);
    },
    [isControlled, onChange]
  );

  // autosize
  const autosize = React.useCallback(() => {
    const el = taRef.current;
    if (!el) return;
    const styles = window.getComputedStyle(el);
    const lineHeight = parseFloat(styles.lineHeight || "20") || 20;
    const minH = lineHeight * Math.max(1, minRows);
    const maxH = lineHeight * Math.max(minRows, maxRows);

    el.style.height = "auto";
    const next = Math.min(el.scrollHeight, maxH);
    el.style.height = `${Math.max(next, minH)}px`;
    el.style.overflowY = el.scrollHeight > maxH ? "auto" : "hidden";
  }, [minRows, maxRows]);

  React.useLayoutEffect(() => {
    autosize();
  }, [inner, autosize]);

  React.useEffect(() => {
    if (autoFocus) taRef.current?.focus();
  }, [autoFocus]);

  const submit = React.useCallback(async () => {
    if (disabled || sending) return;
    const text = (inner ?? "").trim();
    if (!text) return;
    try {
      setSending(true);
      await onSend(text);
      if (!isControlled) setInner("");
      requestAnimationFrame(() => autosize());
    } finally {
      setSending(false);
      taRef.current?.focus();
    }
  }, [autosize, disabled, inner, isControlled, onSend, sending]);

  const onKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (composingRef.current) return;
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      void submit();
    }
  };

  // --- Suggestions visibility logic
  const shouldShowSuggestions = React.useMemo(() => {
    if (!suggestions.length) return false;
    if (showSuggestionsWhen === "never") return false;
    if (showSuggestionsWhen === "always") return true;
    if (showSuggestionsWhen === "focused") return focused;
    // "empty" (default)
    return !inner.trim();
  }, [suggestions.length, showSuggestionsWhen, focused, inner]);

  const handleSuggestionClick = async (s: Suggestion) => {
    onSuggestionSelect?.(s);

    if (suggestionMode === "send") {
      if (disabled || sending) return;
      try {
        setSending(true);
        await onSend(s.text);
        if (!isControlled) setInner("");
        requestAnimationFrame(() => autosize());
      } finally {
        setSending(false);
        taRef.current?.focus();
      }
      return;
    }

    // insert mode
    const next = s.text;
    if (isControlled) onChange?.(next);
    else setInner(next);
    requestAnimationFrame(() => {
      taRef.current?.focus();
      autosize();
    });
  };

  return (
    <TooltipProvider delayDuration={200}>
      <div className="space-y-2">
        {shouldShowSuggestions && (
          <div
            className="flex flex-wrap items-center gap-2 px-1 text-xs text-muted-foreground"
            aria-label="Suggested questions"
          >
            <Sparkles className="h-3.5 w-3.5 opacity-70" />
            {suggestions.map((s) => (
              <Button
                key={s.id ?? s.text}
                type="button"
                variant="secondary"
                size="sm"
                className="rounded-full px-3 py-1 h-7 text-xs"
                onClick={() => void handleSuggestionClick(s)}
                disabled={disabled || sending}
              >
                {s.text}
              </Button>
            ))}
          </div>
        )}

        <div
          className={[
            "w-full rounded-2xl border bg-background shadow-sm",
            "flex items-end gap-2 p-2 md:p-3",
            className,
          ].join(" ")}
          aria-disabled={disabled || sending}
        >
          {showAttach && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 rounded-xl"
                  onClick={onAttachClick}
                  disabled={disabled || sending}
                >
                  <Paperclip className="h-5 w-5" />
                  <span className="sr-only">Attach</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Attach file</TooltipContent>
            </Tooltip>
          )}

          <div className="flex-1">
            <textarea
              ref={taRef}
              value={inner}
              onChange={(e) => setText(e.target.value)}
              onKeyDown={onKeyDown}
              onCompositionStart={() => (composingRef.current = true)}
              onCompositionEnd={() => (composingRef.current = false)}
              onFocus={() => setFocused(true)}
              onBlur={() => setFocused(false)}
              placeholder={placeholder}
              disabled={disabled || sending}
              rows={minRows}
              aria-label={ariaLabel ?? "Chat message input"}
              className={[
                "w-full resize-none bg-transparent",
                "border-0 outline-none",
                "text-sm md:text-base leading-6",
                "placeholder:text-muted-foreground",
                "px-2 py-2 md:px-3 md:py-3",
              ].join(" ")}
              style={{ lineHeight: "1.5" }}
            />
            <div className="flex items-center justify-between px-2 md:px-3 pb-1">
              <p className="text-[11px] text-muted-foreground">
                Send: <kbd className="rounded border px-1">Enter</kbd> • New line:{" "}
                <kbd className="rounded border px-1">Shift</kbd>+
                <kbd className="rounded border px-1">Enter</kbd>
              </p>
              {showLabel && (
                <p className="text-[11px] text-muted-foreground">{label}</p>
              )}
            </div>
          </div>

          {showMic && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 rounded-xl"
                  onClick={onMicClick}
                  disabled={disabled || sending}
                >
                  <Mic className="h-5 w-5" />
                  <span className="sr-only">Dictate</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Dictate</TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                onClick={() => void submit()}
                disabled={disabled || sending || !inner.trim()}
                className="h-9 w-9 rounded-xl"
                size="icon"
              >
                {sending ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
                <span className="sr-only">Send</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Send</TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
};
