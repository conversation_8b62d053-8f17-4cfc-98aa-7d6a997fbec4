import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface ProjectJob {
  id: string;
  status: 'queued' | 'running' | 'done' | 'error';
  progress: number;
  result: any | null;
  error: string | null;
  message: string | null;
  entity_name: string;
  entity_id: string;
  created_at: string;
  updated_at: string;
}

export const useProjectJobs = (projectId: string | null) => {
  const queryClient = useQueryClient();

  const { data: jobs = [], isLoading } = useQuery({
    queryKey: ['project-jobs', projectId],
    queryFn: async () => {
      if (!projectId) return [];

      const { data, error } = await supabase
        .from('jobs')
        .select('*')
        .eq('entity_name', 'text_overlay_projects')
        .eq('entity_id', projectId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as ProjectJob[];
    },
    enabled: !!projectId,
  });

  // Set up real-time subscriptions
  useEffect(() => {
    if (!projectId) return;

    const channel = supabase
      .channel(`project-jobs-${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'jobs',
          filter: `entity_name=eq.text_overlay_projects,entity_id=eq.${projectId}`,
        },
        () => {
          // Invalidate and refetch jobs when any change occurs
          queryClient.invalidateQueries({ queryKey: ['project-jobs', projectId] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [projectId, queryClient]);

  const activeJobs = jobs.filter(job => 
    job.status === 'queued' || job.status === 'running'
  );

  const completedJobs = jobs.filter(job => job.status === 'done');
  const errorJobs = jobs.filter(job => job.status === 'error');

  return {
    jobs,
    activeJobs,
    completedJobs,
    errorJobs,
    isLoading,
    hasActiveJobs: activeJobs.length > 0,
    hasErrors: errorJobs.length > 0,
  };
};