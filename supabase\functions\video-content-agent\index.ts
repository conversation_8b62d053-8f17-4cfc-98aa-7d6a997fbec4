import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import "https://deno.land/x/xhr@0.1.0/mod.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // Get auth token from request
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { 
      campaign_id, 
      task, 
      platform, 
      video_length, 
      tone, 
      target_audience,
      key_message,
      call_to_action 
    } = await req.json();

    if (!campaign_id || !task) {
      return new Response(
        JSON.stringify({ error: 'campaign_id and task are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Video content agent processing task:', task, 'for campaign:', campaign_id);

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('*')
      .eq('id', campaign_id)
      .eq('user_id', user.id)
      .single();

    if (campaignError || !campaign) {
      return new Response(
        JSON.stringify({ error: 'Campaign not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Log the conversation
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'video_content',
        role: 'user',
        message: `Video content task: ${task}`,
        context_data: { platform, video_length, tone, target_audience }
      });

    let systemPrompt = '';
    let userPrompt = '';

    // Define different tasks for the video content agent
    switch (task) {
      case 'create_script':
        systemPrompt = `You are a professional video scriptwriter specializing in marketing content. Create engaging, compelling video scripts that drive action and connect with the target audience. Include scene descriptions, dialogue, and timing cues.`;
        userPrompt = `Campaign: ${campaign.name}
Platform: ${platform || 'social media'}
Video Length: ${video_length || '30-60 seconds'}
Tone: ${tone || 'professional and engaging'}
Target Audience: ${target_audience || 'general audience'}
Key Message: ${key_message || 'extracted from campaign'}
Call to Action: ${call_to_action || 'engage with brand'}

Campaign Data: ${JSON.stringify(campaign.campaign_data, null, 2)}

Create a compelling video script that includes:
1. Hook (first 3 seconds)
2. Main content with clear messaging
3. Strong call to action
4. Scene descriptions and visual cues
5. Timing recommendations`;
        break;

      case 'create_storyboard':
        systemPrompt = `You are a video storyboard artist and creative director. Create detailed storyboards for marketing videos that effectively communicate the visual narrative and support the marketing objectives.`;
        userPrompt = `Campaign: ${campaign.name}
Platform: ${platform || 'social media'}
Video Length: ${video_length || '30-60 seconds'}
Tone: ${tone || 'professional and engaging'}

Campaign Data: ${JSON.stringify(campaign.campaign_data, null, 2)}

Create a detailed storyboard with:
1. Scene-by-scene breakdown
2. Visual descriptions for each shot
3. Camera angles and movements
4. Text overlays and graphics
5. Transition recommendations
6. Timing for each scene`;
        break;

      case 'generate_concepts':
        systemPrompt = `You are a creative video concept developer for marketing campaigns. Generate innovative and engaging video concepts that align with brand objectives and resonate with target audiences.`;
        userPrompt = `Campaign: ${campaign.name}
Platform: ${platform || 'social media'}
Target Audience: ${target_audience || 'general audience'}
Tone: ${tone || 'professional and engaging'}

Campaign Data: ${JSON.stringify(campaign.campaign_data, null, 2)}

Generate 3-5 unique video concepts including:
1. Concept title and theme
2. Creative approach and style
3. Key visual elements
4. Narrative structure
5. Unique value proposition
6. Platform-specific adaptations`;
        break;

      case 'optimize_for_platform':
        systemPrompt = `You are a platform-specific video marketing expert. Optimize video content strategies for specific social media platforms, considering their unique algorithms, audience behaviors, and best practices.`;
        userPrompt = `Campaign: ${campaign.name}
Target Platform: ${platform}
Video Length: ${video_length}

Campaign Data: ${JSON.stringify(campaign.campaign_data, null, 2)}

Provide platform-specific optimization recommendations including:
1. Optimal video specifications (dimensions, length, format)
2. Platform-specific best practices
3. Content adaptation strategies
4. Hashtag and caption recommendations
5. Posting timing and frequency
6. Engagement strategies`;
        break;

      default:
        systemPrompt = `You are a comprehensive video marketing specialist. Provide expert guidance on video content creation, strategy, and optimization for marketing campaigns.`;
        userPrompt = `Campaign: ${campaign.name}
Task: ${task}
Platform: ${platform || 'social media'}
Video Length: ${video_length || 'flexible'}
Tone: ${tone || 'professional'}

Campaign Data: ${JSON.stringify(campaign.campaign_data, null, 2)}

Provide comprehensive guidance for the requested video content task.`;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 2500,
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0]?.message?.content?.trim();

    if (!aiResponse) {
      throw new Error('Failed to generate video content response');
    }

    // Create or update video project if this is a script/storyboard creation task
    let videoProject = null;
    if (task === 'create_script' || task === 'create_storyboard' || task === 'generate_concepts') {
      const { data: existingProject } = await supabase
        .from('video_projects')
        .select('*')
        .eq('campaign_id', campaign_id)
        .single();

      if (existingProject) {
        // Update existing project
        const updateData: any = { updated_at: new Date().toISOString() };
        
        if (task === 'create_script') {
          updateData.script = aiResponse;
        } else if (task === 'create_storyboard') {
          updateData.storyboard = { content: aiResponse, created_at: new Date().toISOString() };
        } else if (task === 'generate_concepts') {
          updateData.video_concept = { concepts: aiResponse, created_at: new Date().toISOString() };
        }

        const { data: updatedProject, error: updateError } = await supabase
          .from('video_projects')
          .update(updateData)
          .eq('id', existingProject.id)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating video project:', updateError);
        } else {
          videoProject = updatedProject;
        }
      } else {
        // Create new project
        const projectData: any = {
          campaign_id,
          title: `${campaign.name} - Video Project`,
          status: 'concept',
          platform_specs: {
            platform: platform || 'social_media',
            video_length,
            tone,
            target_audience
          }
        };

        if (task === 'create_script') {
          projectData.script = aiResponse;
          projectData.status = 'script_ready';
        } else if (task === 'create_storyboard') {
          projectData.storyboard = { content: aiResponse, created_at: new Date().toISOString() };
          projectData.status = 'storyboard_ready';
        } else if (task === 'generate_concepts') {
          projectData.video_concept = { concepts: aiResponse, created_at: new Date().toISOString() };
        }

        const { data: newProject, error: createError } = await supabase
          .from('video_projects')
          .insert(projectData)
          .select()
          .single();

        if (createError) {
          console.error('Error creating video project:', createError);
        } else {
          videoProject = newProject;
        }
      }
    }

    // Log the AI response
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'video_content',
        role: 'assistant',
        message: aiResponse,
        context_data: { 
          task, 
          platform,
          video_project_id: videoProject?.id,
          model_used: 'gpt-4o-mini' 
        }
      });

    // Update campaign with the current agent
    await supabase
      .from('marketing_campaigns')
      .update({
        current_agent: 'video_content',
        updated_at: new Date().toISOString()
      })
      .eq('id', campaign_id);

    console.log('Video content agent completed task successfully');

    return new Response(JSON.stringify({
      success: true,
      response: aiResponse,
      task,
      campaign_id,
      video_project_id: videoProject?.id,
      agent_type: 'video_content'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in video-content-agent function:', error);
    
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to process video content task'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});