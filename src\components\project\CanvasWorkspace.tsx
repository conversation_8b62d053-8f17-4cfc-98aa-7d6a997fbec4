import React, { useRef, useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RotateCcw, Move } from 'lucide-react';

import { Database } from '@/integrations/supabase/types';

type Project = Database['public']['Tables']['creative_projects']['Row'];

interface CanvasWorkspaceProps {
  project: Project;
  selectedElement: string | null;
  onElementSelect: (elementId: string | null) => void;
}

const CanvasWorkspace = ({ project, selectedElement, onElementSelect }: CanvasWorkspaceProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [zoom, setZoom] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const formatPreset = project.format_preset as any || { name: 'custom', width: 1080, height: 1080 };
  const { width, height } = formatPreset;
  const canvasWidth = width;
  const canvasHeight = height;

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // Clear canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Draw grid
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    
    const gridSize = 20;
    for (let x = 0; x <= canvasWidth; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvasHeight);
      ctx.stroke();
    }
    for (let y = 0; y <= canvasHeight; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvasWidth, y);
      ctx.stroke();
    }

    // Draw placeholder text
    ctx.fillStyle = '#666666';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Your Design Goes Here', canvasWidth / 2, canvasHeight / 2);

    // Draw sample elements based on canvas_data
    const canvasData = project.canvas_data as any;
    if (canvasData?.elements) {
      canvasData.elements.forEach((element: any) => {
        if (element.type === 'text') {
          ctx.fillStyle = element.color || '#000000';
          ctx.font = `${element.fontSize || 16}px ${element.fontFamily || 'Arial'}`;
          ctx.fillText(element.text || 'Sample Text', element.x || 50, element.y || 50);
        }
      });
    }
  }, [project, canvasWidth, canvasHeight]);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.3));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) / zoom;
    const y = (e.clientY - rect.top) / zoom;

    // For now, just log the click position
    console.log('Canvas clicked at:', { x, y });
    onElementSelect(null);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    // Handle canvas panning if needed
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <Card className="h-full bg-card border-border rounded-lg shadow-sm">
      <div className="p-4 h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-card-foreground">Canvas</h2>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="text-sm text-muted-foreground min-w-[60px] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleResetZoom}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-auto bg-muted rounded-lg p-4 flex items-center justify-center">
          <div 
            className="relative bg-white shadow-lg rounded-lg overflow-hidden"
            style={{ 
              transform: `scale(${zoom})`,
              transformOrigin: 'center center'
            }}
          >
            <canvas
              ref={canvasRef}
              className="block cursor-crosshair"
              onClick={handleCanvasClick}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                width: `${canvasWidth}px`,
                height: `${canvasHeight}px`
              }}
            />
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
          <span>Format: {(formatPreset.name || 'custom').replace('-', ' ').toUpperCase()}</span>
          <span>{canvasWidth} × {canvasHeight}px</span>
        </div>
      </div>
    </Card>
  );
};

export default CanvasWorkspace;