import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Image, Palette, Settings } from 'lucide-react';
import AssetsPanel from './AssetsPanel';
import CanvasWorkspace from './CanvasWorkspace';
import PropertiesPanel from './PropertiesPanel';

import { Database } from '@/integrations/supabase/types';

type Project = Database['public']['Tables']['creative_projects']['Row'];

interface MobileProjectTabsProps {
  project: Project;
  selectedElement: string | null;
  onElementSelect: (elementId: string | null) => void;
  onElementClose: () => void;
}

const MobileProjectTabs = ({ 
  project, 
  selectedElement, 
  onElementSelect, 
  onElementClose 
}: MobileProjectTabsProps) => {
  return (
    <div className="h-full flex flex-col">
      <Tabs defaultValue="canvas" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 m-2">
          <TabsTrigger value="assets" className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Assets
          </TabsTrigger>
          <TabsTrigger value="canvas" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Canvas
          </TabsTrigger>
          <TabsTrigger value="properties" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Properties
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="assets" className="flex-1 m-2 mt-0">
          <AssetsPanel 
            projectId={project.id}
            onElementAdd={onElementSelect}
          />
        </TabsContent>
        
        <TabsContent value="canvas" className="flex-1 m-2 mt-0">
          <CanvasWorkspace
            project={project}
            selectedElement={selectedElement}
            onElementSelect={onElementSelect}
          />
        </TabsContent>
        
        <TabsContent value="properties" className="flex-1 m-2 mt-0">
          {selectedElement ? (
            <PropertiesPanel
              projectId={project.id}
              selectedElement={selectedElement}
              onClose={onElementClose}
            />
          ) : (
            <div className="h-full bg-card rounded-lg shadow-sm flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <Settings className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Select an element to edit properties</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MobileProjectTabs;