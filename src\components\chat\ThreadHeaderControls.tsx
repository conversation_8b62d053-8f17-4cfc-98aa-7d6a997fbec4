// ThreadHeaderControls.tsx
import * as React from "react";
import { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  AlertDialogFooter,
} from "@/components/ui/alert-dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Plus,
  ChevronsUpDown,
  RefreshCcw,
  MoreHorizontal,
  Delete as DeleteIcon,
  Trash2 as Trash2Icon,
  Loader2,
  Search,
} from "lucide-react";
import { cn } from "@/lib/utils";

type Thread = {
  thread_id: string;
  status?: "idle" | "busy" | "interrupted" | "error";
  created_at?: string;
  updated_at?: string;
};

type LastRunByThread = Record<
  string,
  {
    assistant_id?: string;
    created_at?: string;
  }
>;

type Props = {
  threadId?: string | null;
  threadsList: Thread[];
  lastRunByThread: LastRunByThread;
  loadingThreads?: boolean;
  showDebug: boolean;
  setShowDebug: (v: boolean) => void;

  LANGGRAPH_ASSISTANT_ID: string;

  // actions
  createNewThread: () => void | Promise<void>;
  onThreadId: (id: string) => void | Promise<void>;
  fetchThreads: () => void | Promise<void>;
  deleteThread: () => void | Promise<void>;
  deleteAllThreads: () => void | Promise<void>;

  // utils you already have
  timeAgoOnlyIfOver: (date?: string) => string | null | undefined;
};

function statusColor(s?: Thread["status"]) {
  if (s === "idle") return "bg-green-500";
  if (s === "busy") return "bg-yellow-500";
  if (s === "interrupted") return "bg-orange-500";
  if (s === "error") return "bg-red-500";
  return "bg-muted-foreground/50";
}

const StatusDot: React.FC<{ status?: Thread["status"] }> = ({ status }) => (
  <span className={cn("inline-block h-2 w-2 rounded-full", statusColor(status))} />
);

export const ThreadHeaderControls: React.FC<Props> = ({
  threadId,
  threadsList,
  lastRunByThread,
  loadingThreads,
  showDebug,
  setShowDebug,
  LANGGRAPH_ASSISTANT_ID,
  createNewThread,
  onThreadId,
  fetchThreads,
  deleteThread,
  deleteAllThreads,
  timeAgoOnlyIfOver,
}) => {
  const [openPicker, setOpenPicker] = useState(false);
  const [query, setQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // derive current thread label
  const currentLabel = useMemo(() => {
    if (!threadId) return "New thread";
    return `Thread: ${threadId.slice(0, 8)}…`;
  }, [threadId]);

  // filter threads by search
  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    if (!q) return threadsList;
    return threadsList.filter((t) =>
      t.thread_id.toLowerCase().includes(q)
    );
  }, [threadsList, query]);

  // keyboard shortcuts
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      // n => new thread
      if ((e.key === "n" || e.key === "N") && !(e.metaKey || e.ctrlKey || e.altKey)) {
        e.preventDefault();
        createNewThread();
      }
      // / => focus search inside picker (if open) OR open picker
      if (e.key === "/" && !(e.metaKey || e.ctrlKey || e.altKey)) {
        e.preventDefault();
        setOpenPicker(true);
        setTimeout(() => inputRef.current?.focus(), 10);
      }
      // Cmd/Ctrl+R => refresh threads
      if ((e.key === "r" || e.key === "R") && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        fetchThreads();
      }
    };
    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, [createNewThread, fetchThreads]);

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        {/* Primary: New Thread (visible & obvious) */}
        <Button variant="default" size="sm" onClick={createNewThread}>
          <Plus className="h-4 w-4 mr-2" />
          New thread
        </Button>

        {/* Thread picker: searchable combobox */}
        <Popover open={openPicker} onOpenChange={setOpenPicker}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="w-[280px] justify-between"
              aria-label="Select thread"
            >
              <span className="truncate">{currentLabel}</span>
              <ChevronsUpDown className="h-4 w-4 opacity-60" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-[360px]" align="start">
            <Command shouldFilter={false}>
              <div className="flex items-center px-2 py-2">
                <Search className="h-4 w-4 mr-2 opacity-60" />
                <CommandInput
                  ref={inputRef}
                  value={query}
                  onValueChange={setQuery}
                  placeholder="Search threads (press /)"
                />
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-auto h-7 w-7"
                      onClick={() => fetchThreads()}
                      disabled={loadingThreads}
                    >
                      {loadingThreads ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCcw className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Refresh (⌘/Ctrl+R)</TooltipContent>
                </Tooltip>
              </div>
              <CommandList className="max-h-80">
                <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                  No threads found
                </CommandEmpty>
                <CommandGroup heading="Threads">
                  {/* Quick action: create */}
                  <CommandItem
                    onSelect={() => {
                      setOpenPicker(false);
                      createNewThread();
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create new thread
                  </CommandItem>

                  {filtered.map((t) => {
                    const lr = lastRunByThread[t.thread_id];
                    const assistant = lr?.assistant_id ?? LANGGRAPH_ASSISTANT_ID;
                    const when =
                      timeAgoOnlyIfOver(t.updated_at ?? t.created_at ?? lr?.created_at) ||
                      new Date(
                        (t.updated_at ?? t.created_at ?? lr?.created_at) || ""
                      ).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

                    const isActive = t.thread_id === threadId;

                    return (
                      <CommandItem
                        key={t.thread_id}
                        value={t.thread_id}
                        onSelect={() => {
                          setOpenPicker(false);
                          onThreadId(t.thread_id);
                        }}
                        className={cn(
                          "flex flex-col items-start gap-0.5 py-2",
                          isActive && "bg-muted"
                        )}
                      >
                        <div className="flex w-full items-center justify-between">
                          <span className="font-medium truncate">{t.thread_id}</span>
                          <div className="flex items-center gap-1">
                            <StatusDot status={t.status} />
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-[11px] text-muted-foreground">
                          <Badge variant="secondary" className="h-5 px-2">
                            {assistant}
                          </Badge>
                          {t.status ? <span>{t.status}</span> : null}
                          {when ? <span>• {when}</span> : null}
                        </div>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {/* Lightweight refresh (for muscle memory) */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={fetchThreads}
              disabled={loadingThreads}
              aria-label="Refresh threads"
            >
              {loadingThreads ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCcw className="h-4 w-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>Refresh (⌘/Ctrl+R)</TooltipContent>
        </Tooltip>

        {/* Debug toggle (de-emphasized, still handy) */}
        <div className="flex items-center gap-2 pl-2 ml-1 border-l">
          <Label htmlFor="debug" className="text-xs">
            Debug
          </Label>
          <Switch id="debug" checked={showDebug} onCheckedChange={setShowDebug} />
        </div>

        {/* More / destructive in safe place */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="h-8 w-8" aria-label="More actions">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={createNewThread}>
              <Plus className="h-4 w-4 mr-2" /> New thread (N)
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {/* Delete current thread with confirm */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  disabled={!threadId}
                  onSelect={(e) => e.preventDefault()}
                  className={cn("text-destructive", !threadId && "opacity-60")}
                >
                  <DeleteIcon className="h-4 w-4 mr-2" /> Delete selected
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete selected thread?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. We’ll permanently remove the selected
                    thread and its associated runs.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    onClick={() => deleteThread()}
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Delete ALL with extra friction */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  onSelect={(e) => e.preventDefault()}
                  className="text-destructive"
                >
                  <Trash2Icon className="h-4 w-4 mr-2" /> Delete all threads…
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete ALL threads?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently remove every thread. Type <b>DELETE</b> to confirm.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <ConfirmAll onConfirm={deleteAllThreads} />
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </TooltipProvider>
  );
};

// Separate mini-component for the "type DELETE to confirm" pattern
const ConfirmAll: React.FC<{ onConfirm: () => void | Promise<void> }> = ({ onConfirm }) => {
  const [text, setText] = useState("");
  const canDelete = text.trim().toUpperCase() === "DELETE";
  return (
    <div className="flex flex-col gap-3">
      <input
        className="w-full rounded-md border bg-background px-3 py-2 text-sm outline-none"
        placeholder='Type "DELETE" to confirm'
        value={text}
        onChange={(e) => setText(e.target.value)}
      />
      <div className="flex justify-end gap-2">
        <AlertDialogCancel>Cancel</AlertDialogCancel>
        <AlertDialogAction
          disabled={!canDelete}
          className={cn(
            "bg-destructive text-destructive-foreground hover:bg-destructive/90",
            !canDelete && "opacity-60"
          )}
          onClick={onConfirm}
        >
          Delete all
        </AlertDialogAction>
      </div>
    </div>
  );
};
