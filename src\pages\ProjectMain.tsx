import { useState, useC<PERSON>back, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Upload, Camera, Sparkles, Type, Copy, Video, Share2 } from "lucide-react";
import { ImageUpload } from "@/components/ImageUpload";
import { TextProperties, TextOptions } from "@/components/TextProperties";
import { CanvasEditor } from "@/components/CanvasEditor";
import { ProjectBrowser } from "@/components/ProjectBrowser";
import { StockImageBrowser } from "@/components/StockImageBrowser";
import { AIImageGenerator } from "@/components/AIImageGenerator";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/AuthContext";
import { useCampaigns, TextOverlayProject } from "@/hooks/useCampaigns";
import { toast } from "sonner";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import NotebookHeader from "@/components/dashboard/NotebookHeader";
import { useCampaignParams } from "@/utils/urlParams";
import { TextItemsList } from "@/components/TextItemsList";
import { NewProjectOptionsModal } from "@/components/NewProjectOptionsModal";
import { useUCGVideo } from "@/hooks/useUCGVideo";
import UCGVideoModal from "@/components/automation/UCGVideoModal";
import { ProjectJobsStatus } from "@/components/ProjectJobsStatus";
import SocialMediaModal from "@/components/automation/SocialMediaModal";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

import { Image} from "lucide-react"


interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  visible?: boolean;
  left?: number;
  top?: number;
  rotation?: number; // Add rotation property
  fontWeight?: string;
  fontStyle?: string;
  textDecoration?: string;
  stroke?: string;
  strokeWidth?: number;
  shadow?: {
    blur: number;
    offsetX: number;
    offsetY: number;
    color: string;
  };
}

const ProjectMain = () => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [projectTitle, setProjectTitle] = useState<string>("Untitled Project");
  const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
  const [addTextToCanvas, setAddTextToCanvas] = useState<
    ((textOptions: TextOptions) => void) | null
  >(null);
  const [updateTextOnCanvas, setUpdateTextOnCanvas] = useState<
    ((textOptions: TextOptions) => void) | null
  >(null);
  const [deleteTextFromCanvas, setDeleteTextFromCanvas] = useState<
    (() => void) | null
  >(null);
  const [getCanvasData, setGetCanvasData] = useState<(() => any) | null>(null);
  const [selectTextByIndex, setSelectTextByIndex] = useState<((index: number) => void) | null>(null);
  const [selectedTextOptions, setSelectedTextOptions] =
    useState<TextOptions | null>(null);
  const [textItems, setTextItems] = useState<TextItem[]>([]);
  const [selectedTextItemId, setSelectedTextItemId] = useState<string | null>(
    null
  );
  const [currentProject, setCurrentProject] = useState<TextOverlayProject>({
    title: "",
    image_url: "",
    text_objects: [],
    format_type: 'custom-original',
    canvas_width: 800,
    canvas_height: 600,
  });
  const { user, loading } = useAuth();
  const { saveProject, isSaving, useLoadProject, duplicateProject, isDuplicating } = useCampaigns();
  const {
    openModal,
    startUCGVideoCreation,
    isModalOpen,
    closeModal,
    currentImageUrl,
    currentPrompt,
    setCurrentPrompt,
    job,
  } = useUCGVideo(currentProjectId);
  // Social Media modal state and canvas image getters
  const [isSocialMediaOpen, setIsSocialMediaOpen] = useState(false);
  const [getCanvasImage, setGetCanvasImage] = useState<((format?: 'png' | 'jpeg') => string | null) | null>(null);
  const [getCroppedCanvasImage, setGetCroppedCanvasImage] = useState<((format?: 'png' | 'jpeg') => string | null) | null>(null);



  const navigate = useNavigate();

  // Use URL parameters for navigation state
  const campaignParams = useCampaignParams();

  // Track if we're currently loading a project to prevent loops
  const isLoadingFromUrl = useRef(false);

  // Use React Query to load project data
  const { data: loadedProject, isLoading: isLoadingProject } = useLoadProject(
    campaignParams.mode === "existing" ? campaignParams.projectId : null
  );

  // Update text items from project data (when loading a project)
  const updateTextItemsFromProjectData = useCallback((projectData: TextOverlayProject) => {
    if (projectData && projectData.text_objects && Array.isArray(projectData.text_objects)) {
      const newTextItems: TextItem[] = projectData.text_objects.map(
        (obj: any, index: number) => ({
          id: `text-${index}-${Date.now()}`,
          text: obj.text || "",
          fontSize: obj.fontSize || 32,
          fontFamily: obj.fontFamily || "Arial",
          color: obj.fill || "#000000",
          x: obj.left || 0,
          y: obj.top || 0,
          width: obj.width || 200,
          height: obj.height || 50,
          rotation: obj.angle || 0,
          fontWeight: obj.fontWeight || "normal",
          fontStyle: obj.fontStyle || "normal",
          textAlign: obj.textAlign || "left",
          textDecoration: obj.textDecoration || "none",
        })
      );
      setTextItems(newTextItems);
      console.log('Updated text items from project data:', newTextItems);
    }
  }, []);

  useEffect(() => {
    if (!loading && !user) {
      navigate("/auth");
    }
  }, [user, loading, navigate]);

  // Handle project initialization from URL parameters
  useEffect(() => {
    const { projectId, mode, projectTitle: urlProjectTitle } = campaignParams;

    // Prevent loading if we're already in the process of loading from URL
    if (isLoadingFromUrl.current) {
      return;
    }

    if (mode === "new" && urlProjectTitle && projectTitle !== urlProjectTitle) {
      setProjectTitle(urlProjectTitle);
      setCurrentProjectId(null);
      setImageUrl("");
      setImageFile(null);
      setCurrentProject({
        title: "",
        image_url: "",
        text_objects: [],
        format_type: 'custom-original',
        canvas_width: 800,
        canvas_height: 600,
      });
      setLoadedProjectData(null);
    }
  }, [
    campaignParams.projectId,
    campaignParams.mode,
    campaignParams.projectTitle,
    currentProjectId,
    projectTitle,
  ]);

  // Show new project options modal when mode=new but no projectTitle
  useEffect(() => {
    const { mode, projectTitle: urlProjectTitle } = campaignParams;

    if (mode === "new" && !urlProjectTitle && campaignParams.modal !== 'new-project-options') {
      campaignParams.setModal('new-project-options');
    }
  }, [campaignParams.mode, campaignParams.projectTitle, campaignParams.modal]);

  // Handle loaded project data from React Query
  useEffect(() => {
    if (loadedProject && campaignParams.mode === "existing" && campaignParams.projectId) {
      // Prevent loading if we're already in the process of loading from URL
      if (isLoadingFromUrl.current) {
        return;
      }

      // Only load if we haven't already loaded this project
      if (currentProjectId !== loadedProject.id) {
        isLoadingFromUrl.current = true;

        setImageUrl(loadedProject.image_url);
        setProjectTitle(loadedProject.title);
        setCurrentProjectId(loadedProject.id);
        setImageFile(null);
        setCurrentProject({
          title: loadedProject.title,
          image_url: loadedProject.image_url,
          text_objects: loadedProject.text_objects || [],
          format_type: loadedProject.format_type || 'custom-original',
          canvas_width: loadedProject.canvas_width || 800,
          canvas_height: loadedProject.canvas_height || 600,
          provider: loadedProject.provider || "upload",
          author_name: loadedProject.author_name,
          author_username: loadedProject.author_username,
          author_profile_url: loadedProject.author_profile_url,
          image_source_id: loadedProject.image_source_id,
          image_source_url: loadedProject.image_source_url,
          image_source_tags: loadedProject.image_source_tags,
          image_bucket_path: loadedProject.image_bucket_path,
          downloaded_at: loadedProject.downloaded_at,
        });
        setLoadedProjectData(loadedProject);
        console.log('ImageGenerator: Set loadedProjectData from React Query:', loadedProject);

        // Update text items list from loaded project data
        updateTextItemsFromProjectData(loadedProject);

        // Prevent canvas updates for a short period to avoid race condition
        setTimeout(() => {
          isLoadingFromUrl.current = false;
        }, 2000);
      }
    }
  }, [loadedProject, campaignParams.mode, campaignParams.projectId, currentProjectId, updateTextItemsFromProjectData]);

  const handleSetAddTextToCanvas = useCallback(
    (fn: (textOptions: TextOptions) => void) => {
      setAddTextToCanvas(() => fn);
    },
    []
  );

  const handleSetUpdateTextOnCanvas = useCallback(
    (fn: (textOptions: TextOptions) => void) => {
      setUpdateTextOnCanvas(() => fn);
    },
    []
  );

  const handleSetDeleteTextFromCanvas = useCallback((fn: () => void) => {
    setDeleteTextFromCanvas(() => fn);
  }, []);

  const handleTextSelected = useCallback((textOptions: TextOptions | null) => {
    setSelectedTextOptions(textOptions);

    // Sync list selection with canvas selection
    if (textOptions?.meta?.index !== undefined && textOptions?.meta?.index !== null) {
      const idx = textOptions.meta.index;
      setTextItems((prev) => {
        if (idx >= 0 && idx < prev.length) {
          // Update the selected list item id to match the canvas' active text object
          setSelectedTextItemId(prev[idx].id);
        }
        return prev;
      });
    }
  }, []);

  // Text items management handlers
  const handleSelectTextItem = useCallback(
    (id: string) => {
      setSelectedTextItemId(id);
      // Let the canvas be the single source of truth for selection properties
      // Find the index and programmatically select the corresponding canvas object
      const index = textItems.findIndex((item) => item.id === id);
      if (selectTextByIndex && index >= 0) {
        selectTextByIndex(index);
      }
      // Do NOT set selectedTextOptions here to avoid property leakage between items.
      // The CanvasEditor will emit onTextSelected with the accurate properties for the active object.
    },
    [textItems, selectTextByIndex]
  );

  const handleToggleTextItemVisibility = useCallback((id: string) => {
    setTextItems((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, visible: !item.visible } : item
      )
    );
  }, []);


  const handleDeleteTextItem = useCallback(
    (id: string) => {
      // Determine the canvas index for this item BEFORE mutating list state
      const index = textItems.findIndex((item) => item.id === id);

      // Programmatically select this item on the canvas so deletion targets the correct object
      if (selectTextByIndex && index >= 0) {
        selectTextByIndex(index);
      }

      // Perform canvas deletion if available (acts on current active object)
      if (deleteTextFromCanvas) {
        deleteTextFromCanvas();
      }

      // Remove from sidebar list
      setTextItems((prev) => prev.filter((item) => item.id !== id));

      // Clear selection if we just deleted the selected item
      if (selectedTextItemId === id) {
        setSelectedTextItemId(null);
        setSelectedTextOptions(null);
      }
    },
    [textItems, selectedTextItemId, deleteTextFromCanvas, selectTextByIndex]
  );

  // Show a confirmation dialog before deleting from list
  const confirmAndDeleteTextItem = useCallback(
    async (id: string) => {
      const ok = window.confirm('Are you sure you want to delete this text item?');
      if (!ok) return;
      handleDeleteTextItem(id);
    },
    [handleDeleteTextItem]
  );

  // Update text items when canvas changes
  const updateTextItemsFromCanvas = useCallback(() => {
    if (getCanvasData && !isLoadingFromUrl.current) {
      const canvasData = getCanvasData();
      if (canvasData && canvasData.textObjects && canvasData.textObjects.length > 0) {
        const now = Date.now();
        setTextItems((prev) => {
          const newTextItems: TextItem[] = canvasData.textObjects.map(
            (obj: any, index: number) => ({
              // Preserve stable IDs across refreshes to keep selection highlight
              id: prev[index]?.id ?? `text-${index}-${now}`,
              text: obj.text || "",
              fontSize: obj.fontSize || 32,
              fontFamily: obj.fontFamily || "Arial",
              color: obj.fill || "#000000",
              visible: prev[index]?.visible ?? true,
              left: obj.left,
              top: obj.top,
              rotation: obj.angle || 0, // Add rotation from angle
              fontWeight: obj.fontWeight,
              fontStyle: obj.fontStyle,
              textDecoration: obj.textDecoration,
              stroke: obj.stroke,
              strokeWidth: obj.strokeWidth,
              shadow: obj.shadow,
            })
          );
          return newTextItems;
        });
      }
      // Don't clear text items if canvas is empty - keep existing items
    }
  }, [getCanvasData]);

  const handleImageUpload = useCallback(
    (
      url: string,
      file?: File,
      imageData?: {
        provider: string;
        authorName?: string;
        authorUsername?: string;
        authorProfileUrl?: string;
        imageSourceId?: string;
        imageSourceUrl?: string;
        imageSourceTags?: string[];
        imageBucketPath?: string;
        downloadedAt?: string;
      }
    ) => {
      setImageUrl(url);
      setImageFile(file || null);

      // Store image data for saving
      if (imageData) {
        setCurrentProject((prev) => ({
          ...prev,
          provider: imageData.provider,
          author_name: imageData.authorName,
          author_username: imageData.authorUsername,
          author_profile_url: imageData.authorProfileUrl,
          image_source_id: imageData.imageSourceId,
          image_source_url: imageData.imageSourceUrl,
          image_source_tags: imageData.imageSourceTags,
          image_bucket_path: imageData.imageBucketPath,
          downloaded_at: imageData.downloadedAt,
        }));
      } else {
        // Reset for uploaded files
        setCurrentProject((prev) => ({
          ...prev,
          provider: "upload",
          author_name: undefined,
          author_username: undefined,
          author_profile_url: undefined,
          image_source_id: undefined,
          image_source_url: undefined,
          image_source_tags: undefined,
          image_bucket_path: undefined,
          downloaded_at: undefined,
        }));
      }
    },
    []
  );

  const handleAddText = useCallback(
    (textOptions: TextOptions) => {
      if (addTextToCanvas && textOptions) {
        addTextToCanvas(textOptions);
      }
    },
    [addTextToCanvas]
  );

  const handleUpdateText = useCallback(
    (textOptions: TextOptions) => {
      if (updateTextOnCanvas && textOptions) {
        updateTextOnCanvas(textOptions);
      }
    },
    [updateTextOnCanvas]
  );

  const handleDeleteText = useCallback(() => {
    if (deleteTextFromCanvas) {
      deleteTextFromCanvas();
    }
  }, [deleteTextFromCanvas]);

  const handleSetGetCanvasData = useCallback((fn: () => any) => {
    setGetCanvasData(() => fn);
  }, []);

  const handleSaveProject = useCallback(async () => {
    if (!getCanvasData) {
      toast.error("Canvas not ready");
      return;
    }

    const canvasData = getCanvasData();

    const project: TextOverlayProject = {
      id: currentProjectId,
      title: projectTitle,
      image_url: imageUrl,
      image_bucket_path:
        currentProject.image_bucket_path ||
        (currentProject.provider === "pixabay"
          ? currentProject.image_bucket_path
          : imageFile
          ? ""
          : ""),
      text_objects: canvasData.textObjects,
      canvas_settings: canvasData.canvasSettings,
      format_type: currentProject.format_type || 'custom-original',
      canvas_width: currentProject.canvas_width || 800,
      canvas_height: currentProject.canvas_height || 600,
      provider: currentProject.provider || "upload",
      author_name: currentProject.author_name,
      author_username: currentProject.author_username,
      author_profile_url: currentProject.author_profile_url,
      image_source_id: currentProject.image_source_id,
      image_source_url: currentProject.image_source_url,
      image_source_tags: currentProject.image_source_tags,
      downloaded_at: currentProject.downloaded_at,
    };

    try {
      const savedId = await saveProject({ project, imageFile: imageFile || undefined });
      if (savedId && !currentProjectId) {
        setCurrentProjectId(savedId);
      }
    } catch (error) {
      console.error('Failed to save project:', error);
    }
  }, [
    getCanvasData,
    currentProjectId,
    projectTitle,
    imageUrl,
    imageFile,
    saveProject,
    currentProject,
  ]);

  const handleDuplicateProject = useCallback(async () => {
    if (!currentProjectId || !imageUrl || !projectTitle.trim()) {
      toast.error("Please save the project first before duplicating");
      return;
    }

    try {
      // Get the current canvas data
      const canvasData = getCanvasData ? getCanvasData() : null;

      if (!canvasData) {
        toast.error("Canvas data not available");
        return;
      }

      const currentProjectData: TextOverlayProject = {
        id: undefined, // No ID for new project
        title: `Copy of ${projectTitle}`,
        image_url: imageUrl,
        image_bucket_path: currentProject.image_bucket_path,
        text_objects: canvasData.textObjects || [],
        canvas_settings: canvasData.canvasSettings || {},
        format_type: currentProject.format_type || 'custom-original',
        canvas_width: currentProject.canvas_width || 800,
        canvas_height: currentProject.canvas_height || 600,
        provider: currentProject.provider,
        author_name: currentProject.author_name,
        author_username: currentProject.author_username,
        author_profile_url: currentProject.author_profile_url,
        image_source_id: currentProject.image_source_id,
        image_source_url: currentProject.image_source_url,
        image_source_tags: currentProject.image_source_tags,
        downloaded_at: currentProject.downloaded_at,
      };

      const newProjectId = await duplicateProject(currentProjectData);

      if (newProjectId) {
        // Navigate to the new duplicated project
        campaignParams.setProjectId(newProjectId);
        setCurrentProjectId(newProjectId);
        setProjectTitle(`Copy of ${projectTitle}`);
        toast.success("Project duplicated successfully!");
      }
    } catch (error) {
      console.error('Error duplicating project:', error);
      toast.error("Failed to duplicate project");
    }
  }, [currentProjectId, imageUrl, projectTitle, currentProject, getCanvasData, duplicateProject, campaignParams]);

  const handleCreateUCGVideo = useCallback(async () => {
    if (!imageUrl) {
      toast.error("Please add an image first before creating a UCG video");
      return;
    }

    openModal(imageUrl);
  }, [imageUrl, openModal]);

  const handleProjectLoad = useCallback(
    async (project: TextOverlayProject) => {
      // Set flag to prevent useEffect from triggering
      isLoadingFromUrl.current = true;

      // Set the project data first
      setImageUrl(project.image_url);
      setProjectTitle(project.title);
      setCurrentProjectId(project.id);
      setImageFile(null); // Clear file since we're loading from URL

      // Set project metadata including all audit data
      setCurrentProject({
        title: project.title,
        image_url: project.image_url,
        text_objects: project.text_objects || [],
        format_type: project.format_type || 'custom-original',
        canvas_width: project.canvas_width || 800,
        canvas_height: project.canvas_height || 600,
        provider: project.provider || "upload",
        author_name: project.author_name,
        author_username: project.author_username,
        author_profile_url: project.author_profile_url,
        image_source_id: project.image_source_id,
        image_source_url: project.image_source_url,
        image_source_tags: project.image_source_tags,
        image_bucket_path: project.image_bucket_path,
        downloaded_at: project.downloaded_at,
      });

      // Store project data to be loaded into canvas
      setLoadedProjectData(project);
      console.log('ImageGenerator: Set loadedProjectData from handleProjectLoad:', project);

      // Update text items list from loaded project data
      updateTextItemsFromProjectData(project);

      // Update URL parameters after setting the state to prevent loops
      campaignParams.setMultiple({
        projectId: project.id,
        mode: "existing",
        projectTitle: project.title,
      });

      // Reset flag after a longer delay to prevent race condition
      setTimeout(() => {
        isLoadingFromUrl.current = false;
      }, 2000);
    },
    [campaignParams, updateTextItemsFromProjectData]
  );

  // Add state for loaded project data
  const [loadedProjectData, setLoadedProjectData] = useState<TextOverlayProject | null>(null);

  // Update text items when canvas data changes
  useEffect(() => {
    const interval = setInterval(() => {
      updateTextItemsFromCanvas();
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [updateTextItemsFromCanvas]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Handlers for new project options modal
  const handleUploadFile = () => {
    // Trigger file upload
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const url = URL.createObjectURL(file);
        handleImageUpload(url, file, {
          provider: 'upload',
          authorName: undefined,
          authorUsername: undefined,
          authorProfileUrl: undefined,
          imageSourceId: undefined,
          imageSourceUrl: undefined,
          imageSourceTags: undefined,
          imageBucketPath: undefined,
          downloadedAt: new Date().toISOString()
        });
      }
    };
    fileInput.click();
  };

  const handleBrowseStock = () => {
    campaignParams.setModal('stock-browser');
  };

  const handleGenerateAI = () => {
    campaignParams.setModal('ai-generator');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <NotebookHeader title={projectTitle} notebookId={currentProjectId} />
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1 min-w-0">
               <div className="flex items-center gap-2 lg:gap-4 mt-2 flex-wrap">
                <Input
                  value={projectTitle}
                  onChange={(e) => setProjectTitle(e.target.value)}
                  className="max-w-[200px] lg:max-w-xs"
                  placeholder="Project title"
                />
                <ProjectJobsStatus projectId={currentProjectId} />
                <ProjectBrowser onProjectLoad={handleProjectLoad} />
                {imageUrl && (
                  <>
                    <Button
                      onClick={handleSaveProject}
                      disabled={isSaving}
                      className="bg-primary hover:bg-primary/90"
                      size="sm"
                    >
                      {isSaving ? "Saving..." : "Save"}
                    </Button>
                    <Button
                      onClick={handleDuplicateProject}
                      disabled={!currentProjectId || isDuplicating}
                      variant="outline"
                      size="sm"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      {isDuplicating ? "Duplicating..." : "Duplicate"}
                    </Button>
                  </>
                )}
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button size="sm" variant="outline" className="flex items-center gap-2">
      <Image className="h-4 w-4" />
      Image Source
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuItem
      onClick={() => {
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = "image/*"
        fileInput.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0]
          if (file) {
            const url = URL.createObjectURL(file)
            handleImageUpload(url, file, {
              provider: "upload",
              authorName: undefined,
              authorUsername: undefined,
              authorProfileUrl: undefined,
              imageSourceId: undefined,
              imageSourceUrl: undefined,
              imageSourceTags: undefined,
              imageBucketPath: undefined,
              downloadedAt: new Date().toISOString(),
            })
          }
        }
        fileInput.click()
      }}
    >
      <Upload className="h-4 w-4 mr-2" />
      Upload
    </DropdownMenuItem>

    <DropdownMenuItem onClick={() => campaignParams.setModal("stock-browser")}>
      <Camera className="h-4 w-4 mr-2" />
      Stock
    </DropdownMenuItem>

    <DropdownMenuItem onClick={() => campaignParams.setModal("ai-generator")}>
      <Sparkles className="h-4 w-4 mr-2" />
      AI
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>


                {/* UCG Video Creation Button */}
                {imageUrl && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCreateUCGVideo}
                      disabled={job?.status === 'queued' || job?.status === 'running'}
                    >
                      <Video className="h-4 w-4 mr-2" />
                      {job?.status === 'queued' || job?.status === 'running' ? "Creating..." : "UCG Video"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsSocialMediaOpen(true)}
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      Social Media
                    </Button>
                  </>
                )}

                {/* Add Text - always visible when image loaded; disabled while selection active */}
                {imageUrl && (
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={!!selectedTextOptions || !addTextToCanvas}
                    onClick={() => {
                      if (selectedTextOptions || !addTextToCanvas) return;
                      const defaultText: TextOptions = {
                        text: "Here your text",
                        fontFamily: "Arial",
                        fontSize: 32,
                        fontWeight: "normal",
                        fontStyle: "normal",
                        textDecoration: "normal",
                        fill: "#000000",
                        background: { enabled: false, color: "#ffffff" },
                      };
                      handleAddText(defaultText);
                    }}
                    title={selectedTextOptions ? "Deselect current text to add a new one" : "Add Text"}
                  >
                    <Type className="h-4 w-4 mr-2" />
                    Add Text
                  </Button>
                )}
              </div>
            </div>

            {/* Image Toolbar - Always visible on tablets and larger */}
            {/* <div className="hidden md:block">
              <ImageToolbar onImageUpload={handleImageUpload} compact />
            </div> */}
          </div>

          {/* Mobile Image Toolbar - Below header on small screens */}
          {/* <div className="md:hidden mt-4 pt-4 border-t border-border">
            <ImageToolbar onImageUpload={handleImageUpload} compact />
          </div> */}
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-6 xl:grid-cols-12 gap-4 relative">
          {/* Left Sidebar - Tools */}
          <div className="md:col-span-1 xl:col-span-3 space-y-4">
            {imageUrl && (
              <div className="animate-slide-in space-y-3">
                {/* Text Properties Editor - Sticky and fills available height */}
                <div className="md:sticky top-[120px] h-[calc(100vh-120px)] overflow-y-auto min-h-0">
                  <TextProperties
                    onAddText={handleAddText}
                    onUpdateText={handleUpdateText}
                    onDeleteText={handleDeleteText}
                    selectedTextOptions={selectedTextOptions}
                  />
                </div>
              </div>
            )}

            {!imageUrl && (
              <div className="lg:hidden animate-fade-in">
                <ImageUpload onImageUpload={handleImageUpload} />
              </div>
            )}
          </div>

          {/* Main Canvas Area */}
          <div className="md:col-span-4 xl:col-span-6 space-y-4">
            <div className="animate-fade-in">
                <CanvasEditor
                  imageUrl={imageUrl}
                  onAddText={handleSetAddTextToCanvas}
                  onUpdateText={handleSetUpdateTextOnCanvas}
                  onDeleteText={handleSetDeleteTextFromCanvas}
                  onTextSelected={handleTextSelected}
                  onGetCanvasData={handleSetGetCanvasData}
                  onGetCanvasImage={(fn) => setGetCanvasImage(() => fn)}
                  onGetCroppedCanvasImage={(fn) => setGetCroppedCanvasImage(() => fn)}
                  onSetSelectTextByIndex={(fn) => setSelectTextByIndex(() => fn)}
                  loadedProjectData={loadedProjectData}
                  onProjectDataLoaded={() => {
                    console.log('ImageGenerator: Canvas finished loading project data, clearing loadedProjectData');
                    setLoadedProjectData(null);
                  }}
                  initialFormat={currentProject.format_type}
                  onFormatChange={(format) => {
                    setCurrentProject(prev => ({
                      ...prev,
                      format_type: format.id,
                      canvas_width: format.width,
                      canvas_height: format.height
                    }));
                  }}
                />
            </div>

            {/* Unsplash Attribution */}
            {currentProject.provider === "unsplash" &&
              currentProject.author_name && (
                <div className="text-xs text-muted-foreground bg-card/50 p-3 rounded-lg border">
                  Photo by{" "}
                  <a
                    href={currentProject.author_profile_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-foreground"
                  >
                    {currentProject.author_name}
                  </a>{" "}
                  on{" "}
                  <a
                    href="https://unsplash.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-foreground"
                  >
                    Unsplash
                  </a>
                </div>
              )}
          </div>

          {/* Right Sidebar - Sticky Text Items List */}
          {imageUrl && (
            <div className="md:col-span-1 xl:col-span-3">
              <div className="md:sticky top-[120px] h-[calc(100vh-120px)] overflow-y-auto min-h-0 border-l border-border bg-card/60 backdrop-blur-sm rounded-lg">
                <div className="p-3">
                  <TextItemsList
                    textItems={textItems}
                    selectedItemId={selectedTextItemId}
                    onSelectItem={handleSelectTextItem}
                    onToggleVisibility={handleToggleTextItemVisibility}
                    onDeleteItem={confirmAndDeleteTextItem}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        {!imageUrl && (
          <div className="mt-12 text-center space-y-4 animate-fade-in">
            <h2 className="text-xl font-semibold text-foreground">
              How to use Text Overlay Editor
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  1
                </div>
                <h3 className="font-semibold">Upload Image</h3>
                <p className="text-muted-foreground text-sm">
                  Drag and drop or click to upload your image
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  2
                </div>
                <h3 className="font-semibold">Customize Text</h3>
                <p className="text-muted-foreground text-sm">
                  Add text with custom fonts, colors, and effects
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  3
                </div>
                <h3 className="font-semibold">Export</h3>
                <p className="text-muted-foreground text-sm">
                  Download your image with text overlays
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* New Project Options Modal */}
      <NewProjectOptionsModal
        onUploadFile={handleUploadFile}
        onBrowseStock={handleBrowseStock}
        onGenerateAI={handleGenerateAI}
      />

      {/* Image Source Modals */}
      {campaignParams.modal === 'stock-browser' && (
        <StockImageBrowser
          onImageSelect={(imageUrl, unsplashData) => {
            handleImageUpload(imageUrl, undefined, unsplashData);
            campaignParams.clearModal();
            toast.success("Stock image selected!");
          }}
          onClose={() => campaignParams.clearModal()}
        />
      )}

      {campaignParams.modal === 'ai-generator' && (
        <AIImageGenerator
          onImageGenerated={(imageUrl, metadata) => {
            handleImageUpload(imageUrl, undefined, {
              provider: metadata.provider,
              authorName: undefined,
              authorUsername: undefined,
              authorProfileUrl: undefined,
              imageSourceId: metadata.prompt,
              imageSourceUrl: undefined,
              imageSourceTags: [metadata.model, metadata.style, metadata.quality],
              imageBucketPath: undefined,
              downloadedAt: metadata.generatedAt
            });
            campaignParams.clearModal();
            toast.success("AI image generated successfully!");
          }}
          onClose={() => campaignParams.clearModal()}
        />
      )}

      {/* Social Media Modal */}
      <SocialMediaModal
        isOpen={isSocialMediaOpen}
        onClose={() => setIsSocialMediaOpen(false)}
        imageDataUrl={getCroppedCanvasImage ? getCroppedCanvasImage('png') : null}
        fullCanvasImageDataUrl={getCanvasImage ? getCanvasImage('png') : null}
        projectId={currentProjectId}
      />

      {/* UCG Video Modal */}
      <UCGVideoModal
        entity_name="text_overlay_projects"
        entity_id={currentProjectId}
        isOpen={isModalOpen}
        onClose={closeModal}
        imageUrl={currentImageUrl}
        prompt={currentPrompt}
        onPromptChange={setCurrentPrompt}
        onStartCreation={startUCGVideoCreation}
        job={job}
      />
    </div>
  );
};

export default ProjectMain;
