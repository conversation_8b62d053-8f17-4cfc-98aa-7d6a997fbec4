import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Area, AreaChart, Bar, Bar<PERSON>hart, Line, LineChart, ResponsiveContainer, XAxis, YAxis } from "recharts";
import { TrendingUp, Activity, Clock, AlertTriangle } from "lucide-react";

interface MonitoringChartsProps {
  data?: {
    threadActivity?: Array<{ time: string; threads: number; runs: number }>;
    responseTime?: Array<{ time: string; avgTime: number; p95Time: number }>;
    successRate?: Array<{ time: string; success: number; errors: number }>;
    throughput?: Array<{ time: string; requests: number; responses: number }>;
    latency?: Array<{ time: string; min: number; avg: number; max: number; p95: number }>;
    errorBreakdown?: Array<{ type: string; count: number; percentage: number }>;
  };
  isLoading: boolean;
  showDetailed?: boolean;
}

const chartConfig = {
  threads: {
    label: "Active Threads",
    color: "hsl(var(--primary))",
  },
  runs: {
    label: "Runs",
    color: "hsl(var(--primary-foreground))",
  },
  avgTime: {
    label: "Avg Response Time",
    color: "hsl(var(--primary))",
  },
  p95Time: {
    label: "95th Percentile",
    color: "hsl(var(--destructive))",
  },
  success: {
    label: "Success",
    color: "hsl(var(--primary))",
  },
  errors: {
    label: "Errors",
    color: "hsl(var(--destructive))",
  },
  requests: {
    label: "Requests",
    color: "hsl(var(--primary))",
  },
  responses: {
    label: "Responses",
    color: "hsl(var(--muted-foreground))",
  },
};

export function MonitoringCharts({ data, isLoading, showDetailed = false }: MonitoringChartsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-1/3 mb-2" />
                <div className="h-32 bg-muted rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const baseCharts = (
    <>
      {/* Thread Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Thread Activity
          </CardTitle>
          <CardDescription>Active threads and runs over time</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-32">
            <AreaChart data={data?.threadActivity}>
              <XAxis dataKey="time" tick={false} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area 
                type="monotone" 
                dataKey="threads" 
                stackId="1" 
                stroke="hsl(var(--primary))" 
                fill="hsl(var(--primary))" 
                fillOpacity={0.8}
              />
              <Area 
                type="monotone" 
                dataKey="runs" 
                stackId="1" 
                stroke="hsl(var(--muted-foreground))" 
                fill="hsl(var(--muted-foreground))" 
                fillOpacity={0.6}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Response Time */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Response Time
          </CardTitle>
          <CardDescription>Average and 95th percentile response times</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-32">
            <LineChart data={data?.responseTime}>
              <XAxis dataKey="time" tick={false} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line 
                type="monotone" 
                dataKey="avgTime" 
                stroke="hsl(var(--primary))" 
                strokeWidth={2}
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="p95Time" 
                stroke="hsl(var(--destructive))" 
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
              />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Success Rate */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Success Rate
          </CardTitle>
          <CardDescription>Successful vs failed operations</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-32">
            <AreaChart data={data?.successRate}>
              <XAxis dataKey="time" tick={false} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area 
                type="monotone" 
                dataKey="success" 
                stackId="1" 
                stroke="hsl(var(--primary))" 
                fill="hsl(var(--primary))" 
                fillOpacity={0.8}
              />
              <Area 
                type="monotone" 
                dataKey="errors" 
                stackId="2" 
                stroke="hsl(var(--destructive))" 
                fill="hsl(var(--destructive))" 
                fillOpacity={0.8}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </>
  );

  const detailCharts = showDetailed && (
    <>
      {/* Throughput */}
      <Card>
        <CardHeader>
          <CardTitle>Request Throughput</CardTitle>
          <CardDescription>Requests vs responses over time</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-40">
            <BarChart data={data?.throughput}>
              <XAxis dataKey="time" tick={false} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="requests" fill="hsl(var(--primary))" opacity={0.8} />
              <Bar dataKey="responses" fill="hsl(var(--muted-foreground))" opacity={0.6} />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Detailed Latency */}
      <Card>
        <CardHeader>
          <CardTitle>Latency Distribution</CardTitle>
          <CardDescription>Min, average, max, and 95th percentile latency</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-40">
            <LineChart data={data?.latency}>
              <XAxis dataKey="time" tick={false} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line type="monotone" dataKey="min" stroke="hsl(var(--muted-foreground))" strokeWidth={1} dot={false} />
              <Line type="monotone" dataKey="avg" stroke="hsl(var(--primary))" strokeWidth={2} dot={false} />
              <Line type="monotone" dataKey="max" stroke="hsl(var(--secondary))" strokeWidth={1} dot={false} />
              <Line type="monotone" dataKey="p95" stroke="hsl(var(--destructive))" strokeWidth={2} strokeDasharray="5 5" dot={false} />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Error Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Error Types
          </CardTitle>
          <CardDescription>Breakdown of error types</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-40">
            <BarChart data={data?.errorBreakdown} layout="horizontal">
              <XAxis type="number" />
              <YAxis dataKey="type" type="category" width={80} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="count" fill="hsl(var(--destructive))" opacity={0.8} />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </>
  );

  if (showDetailed) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {baseCharts}
        {detailCharts}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4">
      {baseCharts}
    </div>
  );
}