import { FormatPreset } from '../constants/formatPresets';

export interface OverlayDimensions {
  overlayWidth: number;
  overlayHeight: number;
  offsetX: number;
  offsetY: number;
  scale: number;
  cropWidth: number;
  cropHeight: number;
  cropOffsetX: number;
  cropOffsetY: number;
}

export interface CanvasInfo {
  width: number;
  height: number;
  scale: number;
  containerWidth?: number;
  containerHeight?: number;
}

/**
 * Calculate overlay dimensions and positioning based on canvas and format dimensions
 * Handles different scaling scenarios and responsive behavior
 */
export const calculateOverlayDimensions = (
  canvas: CanvasInfo,
  format: FormatPreset
): OverlayDimensions => {
  const { width: canvasWidth, height: canvasHeight, scale: canvasScale = 1 } = canvas;
  const { width: formatWidth, height: formatHeight } = format;
  
  // Calculate the aspect ratios
  const canvasAspectRatio = canvasWidth / canvasHeight;
  const formatAspectRatio = formatWidth / formatHeight;
  
  let cropWidth: number;
  let cropHeight: number;
  let cropOffsetX: number = 0;
  let cropOffsetY: number = 0;
  
  // Determine how the format fits within the canvas
  if (formatAspectRatio > canvasAspectRatio) {
    // Format is wider than canvas - fit to canvas width
    cropWidth = canvasWidth;
    cropHeight = canvasWidth / formatAspectRatio;
    cropOffsetY = (canvasHeight - cropHeight) / 2;
  } else {
    // Format is taller than canvas - fit to canvas height
    cropHeight = canvasHeight;
    cropWidth = canvasHeight * formatAspectRatio;
    cropOffsetX = (canvasWidth - cropWidth) / 2;
  }
  
  return {
    overlayWidth: canvasWidth,
    overlayHeight: canvasHeight,
    offsetX: 0,
    offsetY: 0,
    scale: canvasScale,
    cropWidth,
    cropHeight,
    cropOffsetX,
    cropOffsetY
  };
};

/**
 * Calculate the best fit scale for a canvas within a container
 */
export const calculateCanvasScale = (
  canvasWidth: number,
  canvasHeight: number,
  containerWidth: number,
  containerHeight: number,
  maxScale: number = 1,
  padding: number = 40
): number => {
  const availableWidth = containerWidth - padding;
  const availableHeight = containerHeight - padding;
  
  const scaleX = availableWidth / canvasWidth;
  const scaleY = availableHeight / canvasHeight;
  
  return Math.min(scaleX, scaleY, maxScale);
};

/**
 * Get crop area coordinates for export
 */
export const getCropCoordinates = (
  canvas: CanvasInfo,
  format: FormatPreset
): { x: number; y: number; width: number; height: number } => {
  const dimensions = calculateOverlayDimensions(canvas, format);
  
  return {
    x: dimensions.cropOffsetX,
    y: dimensions.cropOffsetY,
    width: dimensions.cropWidth,
    height: dimensions.cropHeight
  };
};

/**
 * Check if a format fits entirely within the canvas
 */
export const formatFitsInCanvas = (
  canvasWidth: number,
  canvasHeight: number,
  format: FormatPreset
): boolean => {
  const canvasAspectRatio = canvasWidth / canvasHeight;
  const formatAspectRatio = format.width / format.height;
  
  if (formatAspectRatio > canvasAspectRatio) {
    // Format is wider - check if height fits when width is matched
    const scaledHeight = canvasWidth / formatAspectRatio;
    return scaledHeight <= canvasHeight;
  } else {
    // Format is taller - check if width fits when height is matched
    const scaledWidth = canvasHeight * formatAspectRatio;
    return scaledWidth <= canvasWidth;
  }
};

/**
 * Calculate grid lines for rule of thirds overlay
 */
export const calculateGridLines = (
  cropWidth: number,
  cropHeight: number,
  cropOffsetX: number,
  cropOffsetY: number
): {
  verticalLines: number[];
  horizontalLines: number[];
} => {
  const verticalLines = [
    cropOffsetX + cropWidth / 3,
    cropOffsetX + (cropWidth * 2) / 3
  ];
  
  const horizontalLines = [
    cropOffsetY + cropHeight / 3,
    cropOffsetY + (cropHeight * 2) / 3
  ];
  
  return { verticalLines, horizontalLines };
};

/**
 * Calculate safe area margins for different social media platforms
 */
export const getSafeAreaMargins = (format: FormatPreset): {
  top: number;
  bottom: number;
  left: number;
  right: number;
} => {
  // Default safe area margins as percentages
  const defaultMargins = { top: 0.05, bottom: 0.05, left: 0.05, right: 0.05 };
  
  // Platform-specific safe areas
  const platformMargins: Record<string, typeof defaultMargins> = {
    'Instagram': { top: 0.1, bottom: 0.15, left: 0.05, right: 0.05 },
    'TikTok': { top: 0.15, bottom: 0.2, left: 0.05, right: 0.05 },
    'Facebook': { top: 0.08, bottom: 0.08, left: 0.05, right: 0.05 },
    'Twitter/X': { top: 0.05, bottom: 0.05, left: 0.05, right: 0.05 },
    'LinkedIn': { top: 0.08, bottom: 0.08, left: 0.05, right: 0.05 }
  };
  
  const margins = platformMargins[format.platform] || defaultMargins;
  
  return {
    top: format.height * margins.top,
    bottom: format.height * margins.bottom,
    left: format.width * margins.left,
    right: format.width * margins.right
  };
};

/**
 * Convert overlay coordinates to canvas coordinates
 */
export const overlayToCanvasCoordinates = (
  overlayX: number,
  overlayY: number,
  dimensions: OverlayDimensions
): { x: number; y: number } => {
  return {
    x: (overlayX - dimensions.cropOffsetX) * (dimensions.scale || 1),
    y: (overlayY - dimensions.cropOffsetY) * (dimensions.scale || 1)
  };
};

/**
 * Convert canvas coordinates to overlay coordinates
 */
export const canvasToOverlayCoordinates = (
  canvasX: number,
  canvasY: number,
  dimensions: OverlayDimensions
): { x: number; y: number } => {
  return {
    x: canvasX / (dimensions.scale || 1) + dimensions.cropOffsetX,
    y: canvasY / (dimensions.scale || 1) + dimensions.cropOffsetY
  };
};
