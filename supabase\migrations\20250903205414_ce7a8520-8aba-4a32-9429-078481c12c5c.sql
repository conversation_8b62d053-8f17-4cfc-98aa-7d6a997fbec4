-- Enable pgcrypto for gen_random_uuid if not already
create extension if not exists pgcrypto;

create table public.jobs (
  id uuid primary key default gen_random_uuid(),
  status text not null check (status in ('queued','running','done','error')),
  progress int not null default 0,               -- 0..100
  result jsonb,                                  -- final payload
  error text,                                    -- error message if any
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- updated_at trigger
create or replace function public.set_updated_at()
returns trigger language plpgsql as $$
begin
  new.updated_at = now();
  return new;
end $$;

create trigger set_jobs_updated_at
before update on public.jobs
for each row execute function public.set_updated_at();

alter table public.jobs enable row level security;

-- READ: only when the incoming header x-job-id matches the row id
create policy "read own job by header"
on public.jobs for select
to anon
using ( id::text = current_setting('request.headers.x-job-id', true) );

-- WRITE: Only n8n with service key should write.
-- If you want, you can omit a policy for anon for insert/update/delete (default deny).
-- Service role bypasses RLS, so n8n can freely upsert using the service key.