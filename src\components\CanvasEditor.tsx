import { useEffect, useRef, useState, useCallback } from "react";
import { Canvas as FabricCanvas, FabricText, IText as <PERSON>abric<PERSON><PERSON><PERSON>, Fabric<PERSON>mage, Shadow } from "fabric";
import type { TPointerEventInfo } from "fabric";
import { toast } from "sonner";
import { TextOptions } from "./TextProperties";
import { FormatSelector } from "./FormatSelector";
import { FORMAT_PRESETS, FormatPreset, getFormatPresetById } from "../constants/formatPresets";
import { CanvasOverlay } from "./CanvasOverlay";
import { useCanvasOverlay, OverlayMode } from "../hooks/useCanvasOverlay";

interface CanvasEditorProps {
  imageUrl?: string;
  onAddText: (addTextFunction: (textOptions: TextOptions) => void) => void;
  onUpdateText: (updateTextFunction: (textOptions: TextOptions) => void) => void;
  onDeleteText: (deleteTextFunction: () => void) => void;
  onTextSelected?: (textOptions: TextOptions | null) => void;
  onGetCanvasData: (getCanvasDataFunction: () => any) => void;
  // Expose a getter to retrieve the fully rendered canvas as a data URL
  onGetCanvasImage?: (getCanvasImageFunction: (format?: 'png' | 'jpeg') => string | null) => void;
  // Provide a way for parent to programmatically select a text object by index
  onSetSelectTextByIndex?: (selectFn: (index: number) => void) => void;
  loadedProjectData?: any;
  onProjectDataLoaded?: () => void;
  // Format preset support
  initialFormat?: string; // Format preset ID
  onFormatChange?: (format: FormatPreset) => void;
}

export const CanvasEditor = ({
  imageUrl,
  onAddText,
  onUpdateText,
  onDeleteText,
  onTextSelected,
  onGetCanvasData,
  onGetCanvasImage,
  onSetSelectTextByIndex,
  loadedProjectData,
  onProjectDataLoaded,
  initialFormat = 'custom-original',
  onFormatChange
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<FabricCanvas | null>(null);
  const [selectedTextObject, setSelectedTextObject] = useState<FabricIText | null>(null);
  const [currentFormat, setCurrentFormat] = useState<FormatPreset>(
    getFormatPresetById(initialFormat) || FORMAT_PRESETS.find(p => p.id === 'custom-original')!
  );

  // Canvas overlay state management
  const overlay = useCanvasOverlay(currentFormat, false, 'crop');

  // Keyboard shortcut for overlay toggle
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'o' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        overlay.toggleOverlay();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [overlay]);

  // Render object's backgroundColor without applying the text shadow
  const patchBackgroundNoShadow = (obj: FabricText | FabricIText) => {
    const anyObj = obj as any;
    const orig = anyObj._renderBackground;
    if (typeof orig !== 'function' || orig.__noShadowPatched) return;
    const wrapped = function (this: any, ctx: CanvasRenderingContext2D) {
      let didSave = false;
      try {
        if (this.shadow && ctx && typeof (ctx as any).save === 'function') {
          (ctx as any).save();
          (ctx as any).shadowColor = 'rgba(0,0,0,0)';
          (ctx as any).shadowBlur = 0;
          (ctx as any).shadowOffsetX = 0;
          (ctx as any).shadowOffsetY = 0;
          didSave = true;
        }
      } catch {}
      try { return orig.call(this, ctx); }
      finally { if (didSave) try { (ctx as any).restore(); } catch {} }
    };
    wrapped.__noShadowPatched = true;
    anyObj._renderBackground = wrapped;
  };


  // Handle text selection changes
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSelectionChange = useCallback((selectedObject: any) => {
    // Track IText specifically for inline editing
    if (selectedObject && selectedObject instanceof FabricIText) {
      setSelectedTextObject(selectedObject);
    } else {
      setSelectedTextObject(null);
    }

    // Send selection details to the side panel for both IText and Text
    if ((selectedObject && (selectedObject instanceof FabricIText || selectedObject instanceof FabricText))) {
      if (onTextSelected) {
        const t = selectedObject as FabricText;
        // Derive index within text objects using the object's own canvas reference
        const fc = (selectedObject as any)?.canvas as FabricCanvas | undefined;
        const textObjects = fc
          ? fc.getObjects().filter(obj => (obj instanceof FabricIText) || (obj instanceof FabricText)) as FabricText[]
          : [];
        const index = textObjects.indexOf(t);

        // Migrate any legacy background states to backgroundColor
        let bgColor = ((t as any).backgroundColor as string) || '';
        const legacyTextBg = (t as any).textBackgroundColor as string | undefined;
        const legacyRect = (t as any)._bgRectPartner as any | undefined;
        if (!bgColor) {
          if (legacyTextBg) {
            (t as any).backgroundColor = legacyTextBg;
            (t as any).textBackgroundColor = '';
            bgColor = legacyTextBg;
          } else if (legacyRect && legacyRect.fill) {
            (t as any).backgroundColor = legacyRect.fill as string;
            try { (t as any).canvas?.remove(legacyRect); } catch {}
            (t as any)._bgRectPartner = undefined;
            bgColor = (t as any).backgroundColor as string;
          }
          if ((t as any).setCoords) (t as any).setCoords();
          (t as any).canvas?.requestRenderAll();
        }

        console.log('CanvasEditor: handleSelectionChange backgroundColor =', bgColor);
        const textOptions: TextOptions = {
          text: t.text || '',
          fontFamily: t.fontFamily || 'Arial',
          fontSize: t.fontSize || 32,
          fontWeight: String(t.fontWeight || 'normal'),
          fontStyle: t.fontStyle || 'normal',
          textDecoration: (t as any).textDecoration || 'normal',
          fill: (t.fill as string) || '#000000',
          background: bgColor ? { enabled: true, color: bgColor } : { enabled: false, color: '' },
          textAlign: (t as any).textAlign || 'left',
          stroke: t.stroke as string,
          strokeWidth: t.strokeWidth || 0,
          shadow: t.shadow
            ? {
                blur: t.shadow.blur || 0,
                offsetX: t.shadow.offsetX || 0,
                offsetY: t.shadow.offsetY || 0,
                color: t.shadow.color || '#000000',
              }
            : undefined,
          meta: index >= 0 ? { index } : undefined,
        };
        console.log("CanvasEditor: Text selected with properties:", textOptions);
        onTextSelected(textOptions);
      }
    } else if (!selectedObject && onTextSelected) {
      console.log("CanvasEditor: Selection cleared or non-text object selected");
      onTextSelected(null);
    }
  }, [onTextSelected]);

  useEffect(() => {
    console.log("CanvasEditor: Canvas initialization useEffect, canvasRef.current:", !!canvasRef.current);
    if (!canvasRef.current) {
      console.log("CanvasEditor: canvasRef.current is null, returning");
      return;
    }

    try {
      console.log("CanvasEditor: Creating FabricCanvas with format:", currentFormat);
      const canvas = new FabricCanvas(canvasRef.current, {
        width: currentFormat.width,
        height: currentFormat.height,
        backgroundColor: "#f8f9fa",
      });

      console.log("CanvasEditor: FabricCanvas created successfully:", !!canvas);

      // Add selection event listeners
      canvas.on('selection:created', (e) => {
        handleSelectionChange(e.selected?.[0]);
      });

      canvas.on('selection:updated', (e) => {
        handleSelectionChange(e.selected?.[0]);
      });

      canvas.on('selection:cleared', () => {
        handleSelectionChange(null);
      });

      setFabricCanvas(canvas);

      return () => {
        console.log("CanvasEditor: Disposing canvas");
        canvas.dispose();
      };
    } catch (error) {
      console.error("CanvasEditor: Error creating FabricCanvas:", error);
    }
  }, [handleSelectionChange, currentFormat]);

  useEffect(() => {
    if (!fabricCanvas || !imageUrl) return;

    // Load image with CORS enabled to allow canvas export
    FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' })
      .then((img) => {
        // Clear existing objects
        fabricCanvas.clear();

        // Scale image to fit canvas while maintaining aspect ratio
        const canvasWidth = fabricCanvas.width || 800;
        const canvasHeight = fabricCanvas.height || 600;

        const imgWidth = img.width || 1;
        const imgHeight = img.height || 1;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        img.scale(scale);

        // Center the image manually
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2
        });
        img.setCoords();

        // Add a visible border and shadow around the image to help distinguish boundaries
        img.set({
          stroke: '#64748b', // Darker gray border color for better visibility
          strokeWidth: 3,     // 3px border width for clear visibility
          shadow: new Shadow({
            color: 'rgba(0, 0, 0, 0.2)', // More visible shadow
            blur: 10,                      // Slightly more blur for better definition
            offsetX: 0,                    // No horizontal offset
            offsetY: 3,                    // Slightly more vertical offset for depth
          }),
        });

        // Make image non-selectable and non-movable
        img.selectable = false;
        img.evented = false;

        fabricCanvas.add(img);
        fabricCanvas.sendObjectToBack(img);
        fabricCanvas.renderAll();

        toast.success("Image loaded successfully!");
      })
      .catch((error) => {
        console.error("Image loading error:", error);
        toast.error("Failed to load image. This might be due to CORS restrictions.");
      });
  }, [fabricCanvas, imageUrl]);

  const addTextToCanvas = useCallback((textOptions: TextOptions) => {
    console.log("CanvasEditor: addTextToCanvas called with:", textOptions);
    if (!fabricCanvas || !textOptions || !textOptions.text) {
      console.log("CanvasEditor: Early return - fabricCanvas:", !!fabricCanvas, "textOptions:", !!textOptions, "text:", textOptions?.text);
      return;
    }

    const fabricText = new FabricIText(textOptions.text, {
      left: 100,
      top: 100,
      fontFamily: textOptions.fontFamily,
      fontSize: textOptions.fontSize,
      fontWeight: textOptions.fontWeight,
      fontStyle: textOptions.fontStyle,
      textDecoration: textOptions.textDecoration,
      fill: textOptions.fill,
      textAlign: (textOptions as any).textAlign || 'left',
      // Use object.backgroundColor for full coverage and shadow separation
      textBackgroundColor: '',
      backgroundColor: textOptions.background?.enabled ? textOptions.background.color : '',
      stroke: textOptions.stroke,
      strokeWidth: textOptions.strokeWidth || 0,
      shadow: textOptions.shadow ? new Shadow({
        color: textOptions.shadow.color,
        blur: textOptions.shadow.blur,
        offsetX: textOptions.shadow.offsetX,
        offsetY: textOptions.shadow.offsetY,
      }) : undefined,
    } as any);

    // Ensure background color draws without shadow
    try { patchBackgroundNoShadow(fabricText); } catch {}

    console.log("CanvasEditor: Text object created:", fabricText);
    console.log("CanvasEditor: Canvas objects count before add:", fabricCanvas.getObjects().length);

    fabricCanvas.add(fabricText);
    fabricText.setCoords();
    fabricCanvas.setActiveObject(fabricText);
    fabricCanvas.requestRenderAll();

    console.log("CanvasEditor: Canvas objects count after add:", fabricCanvas.getObjects().length);
    console.log("CanvasEditor: Canvas objects:", fabricCanvas.getObjects());

    toast.success("Text added to canvas!");
  }, [fabricCanvas]);

  const updateTextOnCanvas = useCallback((textOptions: TextOptions) => {
    console.log("CanvasEditor: updateTextOnCanvas called with:", textOptions);
    if (!fabricCanvas || !selectedTextObject || !textOptions || !textOptions.text) {
      console.log("CanvasEditor: Cannot update - fabricCanvas:", !!fabricCanvas, "selectedTextObject:", !!selectedTextObject, "textOptions:", !!textOptions);
      return;
    }

    // Update the selected text object properties
    selectedTextObject.set({
      text: textOptions.text,
      fontFamily: textOptions.fontFamily,
      fontSize: textOptions.fontSize,
      fontWeight: textOptions.fontWeight,
      fontStyle: textOptions.fontStyle,
      textDecoration: textOptions.textDecoration,
      fill: textOptions.fill,
      textAlign: (textOptions as any).textAlign || 'left',
      textBackgroundColor: '',
      backgroundColor: textOptions.background?.enabled ? textOptions.background.color : '',
      stroke: textOptions.stroke,
      strokeWidth: textOptions.strokeWidth || 0,
      shadow: textOptions.shadow ? new Shadow({
        color: textOptions.shadow.color,
        blur: textOptions.shadow.blur,
        offsetX: textOptions.shadow.offsetX,
        offsetY: textOptions.shadow.offsetY,
      }) : null,
    } as any);

    fabricCanvas.renderAll();
    // console.log("CanvasEditor: Text updated successfully");
    // toast.success("Text updated!");
  }, [fabricCanvas, selectedTextObject]);

  const deleteSelectedText = useCallback(() => {
    if (!fabricCanvas || !selectedTextObject) {
      console.log("CanvasEditor: Cannot delete - fabricCanvas:", !!fabricCanvas, "selectedTextObject:", !!selectedTextObject);
      return;
    }
    // Remove from canvas and clear selection
    fabricCanvas.remove(selectedTextObject);
    fabricCanvas.discardActiveObject();
    fabricCanvas.requestRenderAll();
    setSelectedTextObject(null);
    console.log("CanvasEditor: Text deleted successfully");
    toast.success("Text deleted!");
  }, [fabricCanvas, selectedTextObject]);

  // Auto-adjust text box dimensions on content changes (multi-line support)
  useEffect(() => {
    if (!fabricCanvas) return;
    const onObjectModified = (e: any) => {
      const target = e?.target as any;
      if (target && (target instanceof FabricIText || target instanceof FabricText)) {
        try {
          // Recompute dimensions after content change
          target.initDimensions?.();
          target.setCoords?.();
          fabricCanvas.requestRenderAll();
        } catch {}
      }
    };
    // Listen to text editing/changes
    fabricCanvas.on('text:changed', onObjectModified);
    fabricCanvas.on('object:modified', onObjectModified);
    // Clean up
    return () => {
      fabricCanvas.off('text:changed', onObjectModified);
      fabricCanvas.off('object:modified', onObjectModified);
    };
  }, [fabricCanvas]);

  // Inline editing: click to edit, Enter or outside click to save
  useEffect(() => {
    if (!fabricCanvas) return;

    // Single click: do NOT enter editing; allow drag/resize as normal
    // Use single click only to exit editing when clicking outside
    const handleMouseDown = (e: TPointerEventInfo) => {
      const target = e?.target as unknown;
      if (!(target instanceof FabricIText)) {
        if (selectedTextObject?.isEditing) {
          selectedTextObject.exitEditing();
          fabricCanvas.requestRenderAll();
        }
      }
    };

    // Double click: enter editing mode on IText
    const handleDblClick = (e: TPointerEventInfo) => {
      const target = e?.target as unknown;
      if (target && target instanceof FabricIText) {
        // Ensure object is active so the caret aligns properly
        fabricCanvas.setActiveObject(target);
        if (!target.isEditing) {
          target.enterEditing();
          const len = (target.text?.length ?? 0) as number;
          try {
            target.setSelectionStart(len);
            target.setSelectionEnd(len);
          } catch {
            /* noop */
          }
  // While editing, keep box dimensions up-to-date on every change
  useEffect(() => {
    if (!selectedTextObject) return;
    const handleChanged = () => {
      try {
        (selectedTextObject as any).initDimensions?.();
        selectedTextObject.setCoords?.();
        selectedTextObject.canvas?.requestRenderAll();
      } catch {}
    };
    selectedTextObject.on('changed', handleChanged);
    selectedTextObject.on('editing:entered', handleChanged);
    selectedTextObject.on('editing:exited', handleChanged);
    return () => {
      selectedTextObject.off('changed', handleChanged);
      selectedTextObject.off('editing:entered', handleChanged);
      selectedTextObject.off('editing:exited', handleChanged);
    };
  }, [selectedTextObject]);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (target as any).hiddenTextarea?.focus?.();
        }
      }
    };

    fabricCanvas.on('mouse:down', handleMouseDown);
    fabricCanvas.on('mouse:dblclick', handleDblClick);

    return () => {
      fabricCanvas.off('mouse:down', handleMouseDown);
      fabricCanvas.off('mouse:dblclick', handleDblClick);
    };
  }, [fabricCanvas, selectedTextObject]);

  // Exit editing on Escape key (Enter should insert newline)
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedTextObject?.isEditing) {
        e.preventDefault();
        try {
          selectedTextObject.exitEditing();
          fabricCanvas?.requestRenderAll();
        } catch {}
      }
    };
    document.addEventListener('keydown', onKeyDown);
    return () => document.removeEventListener('keydown', onKeyDown);
  }, [selectedTextObject, fabricCanvas]);

  // Keep side panel in sync while typing and on enter/exit
  useEffect(() => {
    if (!selectedTextObject) return;

    const handleChanged = () => {
      if (onTextSelected) {
        const t = selectedTextObject;
        const fc = (t as any)?.canvas as FabricCanvas | undefined;
        const textObjects = fc
          ? fc.getObjects().filter(obj => (obj instanceof FabricIText) || (obj instanceof FabricText)) as FabricText[]
          : [];
        const index = textObjects.indexOf(t);
        const bgColor = ((t as any).backgroundColor as string) || '';
        console.log('CanvasEditor: on-typing backgroundColor =', bgColor);
        const textOptions: TextOptions = {
          text: t.text || '',
          fontFamily: t.fontFamily || 'Arial',
          fontSize: t.fontSize || 32,
          fontWeight: String(t.fontWeight || 'normal'),
          fontStyle: t.fontStyle || 'normal',
          textDecoration: (t as any).textDecoration || 'normal',
          fill: (t.fill as string) || '#000000',
          background: bgColor ? { enabled: true, color: bgColor } : { enabled: false, color: '' },
          textAlign: (t as any).textAlign || 'left',
          stroke: t.stroke as string,
          strokeWidth: t.strokeWidth || 0,
          shadow: t.shadow
            ? {
                blur: t.shadow.blur || 0,
                offsetX: t.shadow.offsetX || 0,
                offsetY: t.shadow.offsetY || 0,
                color: t.shadow.color || '#000000',
              }
            : undefined,
          meta: index >= 0 ? { index } : undefined,
        };
        onTextSelected(textOptions);
      }
    };

    const handleEditingExited = () => {
      handleChanged();
    };

    selectedTextObject.on('changed', handleChanged);
    selectedTextObject.on('editing:exited', handleEditingExited);

    return () => {
      selectedTextObject.off('changed', handleChanged);
      selectedTextObject.off('editing:exited', handleEditingExited);
    };
  }, [selectedTextObject, onTextSelected]);

  // Expose addTextToCanvas, updateTextOnCanvas, and deleteSelectedText through the props
  useEffect(() => {
    console.log("CanvasEditor: useEffect for onAddText, fabricCanvas:", !!fabricCanvas);
    console.log("CanvasEditor: onAddText prop:", !!onAddText);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up addTextToCanvas function");
      onAddText(addTextToCanvas);
      console.log("CanvasEditor: addTextToCanvas function passed to parent");
    }
  }, [fabricCanvas, onAddText, addTextToCanvas]);

  useEffect(() => {
    console.log("CanvasEditor: useEffect for onUpdateText, fabricCanvas:", !!fabricCanvas);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up updateTextOnCanvas function");
      onUpdateText(updateTextOnCanvas);
      console.log("CanvasEditor: updateTextOnCanvas function passed to parent");
    }
  }, [fabricCanvas, onUpdateText, updateTextOnCanvas]);

  useEffect(() => {
    console.log("CanvasEditor: useEffect for onDeleteText, fabricCanvas:", !!fabricCanvas);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up deleteSelectedText function");
      onDeleteText(deleteSelectedText);
      console.log("CanvasEditor: deleteSelectedText function passed to parent");
    }
  }, [fabricCanvas, onDeleteText, deleteSelectedText]);

  // Allow parent to programmatically select a text object by index among text objects
  const selectTextByIndex = useCallback((index: number) => {
    if (!fabricCanvas) return;
    const textObjects = fabricCanvas.getObjects().filter(obj => (obj instanceof FabricIText) || (obj instanceof FabricText)) as FabricText[];
    const target = textObjects[index];
    if (target) {
      fabricCanvas.setActiveObject(target);
      fabricCanvas.requestRenderAll();
      handleSelectionChange(target);
    }
  }, [fabricCanvas, handleSelectionChange]);

  // Expose the selection function to parent
  useEffect(() => {
    if (onSetSelectTextByIndex) {
      onSetSelectTextByIndex(selectTextByIndex);
    }
  }, [onSetSelectTextByIndex, selectTextByIndex]);

  const getCanvasData = useCallback(() => {
    if (!fabricCanvas) return { textObjects: [], canvasSettings: {} };

    const objects = fabricCanvas.getObjects();
    const textObjects = objects
      .filter(obj => obj.type === 'i-text' || obj.type === 'text')
      .map(obj => {
        const t = obj as FabricText;
        return {
          text: t.text,
          left: t.left,
          top: t.top,
          angle: t.angle, // rotation angle
          fontSize: t.fontSize,
          fontFamily: t.fontFamily,
          fill: t.fill,
          fontWeight: t.fontWeight,
          fontStyle: t.fontStyle,
          textDecoration: (t as any).textDecoration,
          background: (() => { const color = ((t as any).backgroundColor as string) || ''; return color ? { enabled: true, color } : { enabled: false, color: '' }; })(),
          stroke: t.stroke,
          strokeWidth: t.strokeWidth,
          textAlign: (t as any).textAlign || 'left',
          // scaling and dimensions for persistence
          scaleX: typeof t.scaleX === 'number' ? t.scaleX : 1,
          scaleY: typeof t.scaleY === 'number' ? t.scaleY : 1,
          width: t.width,
          height: t.height,
          shadow: t.shadow
            ? {
                blur: t.shadow.blur,
                offsetX: t.shadow.offsetX,
                offsetY: t.shadow.offsetY,
                color: t.shadow.color,
              }
            : undefined,
        };
      });

    return {
      textObjects,
      canvasSettings: {
        width: fabricCanvas.width,
        height: fabricCanvas.height
      }
    };
  }, [fabricCanvas]);

  useEffect(() => {
    if (fabricCanvas) {
      onGetCanvasData(getCanvasData);
    }
  }, [fabricCanvas, onGetCanvasData, getCanvasData]);

  // Load project data when provided
  useEffect(() => {
    if (!fabricCanvas || !loadedProjectData) return;

    console.log('CanvasEditor: Loading project data:', loadedProjectData);
    console.log('CanvasEditor: Text objects to load:', loadedProjectData.text_objects);

    // Add a small delay to ensure canvas is fully ready
    const loadTimeout = setTimeout(() => {
      try {
        // Clear existing text objects (keep background image)
        const objects = fabricCanvas.getObjects();
        objects.forEach((obj) => {
          if (obj instanceof FabricText) {
            fabricCanvas.remove(obj);
          }
        });

        // Load text objects from saved project
        if (loadedProjectData.text_objects && Array.isArray(loadedProjectData.text_objects)) {
          console.log('CanvasEditor: Loading', loadedProjectData.text_objects.length, 'text objects');
          loadedProjectData.text_objects.forEach((textData: any, index: number) => {
            console.log(`CanvasEditor: Loading text object ${index}:`, textData);
            const fabricText = new FabricIText(textData.text || '', {
              left: textData.left || 100,
              top: textData.top || 100,
              angle: textData.angle || 0, // rotation angle
              fontFamily: textData.fontFamily || 'Arial',
              fontSize: textData.fontSize || 32,
              fontWeight: textData.fontWeight || 'normal',
              fontStyle: textData.fontStyle || 'normal',
              textDecoration: textData.textDecoration || 'normal',
              fill: textData.fill || '#000000',
              textBackgroundColor: '',
              backgroundColor: textData.background?.enabled ? textData.background.color : '',
              textAlign: textData.textAlign || 'left',
              stroke: textData.stroke,
              strokeWidth: textData.strokeWidth || 0,
              shadow: textData.shadow ? new Shadow({
                color: textData.shadow.color || '#000000',
                blur: textData.shadow.blur || 0,
                offsetX: textData.shadow.offsetX || 0,
                offsetY: textData.shadow.offsetY || 0,
              }) : undefined,
            } as any);

            // Ensure background draw is shadowless
            try { patchBackgroundNoShadow(fabricText); } catch {}

            // Restore scaling with sensible defaults for backward compatibility
            const sx = typeof textData.scaleX === 'number' && isFinite(textData.scaleX) ? textData.scaleX : 1;
            const sy = typeof textData.scaleY === 'number' && isFinite(textData.scaleY) ? textData.scaleY : 1;
            fabricText.set({ scaleX: sx, scaleY: sy });
            fabricText.setCoords();

            fabricCanvas.add(fabricText);
            console.log('CanvasEditor: Added text object to canvas:', fabricText);

          });
        }

        fabricCanvas.renderAll();
        console.log('CanvasEditor: Canvas rendered, total objects:', fabricCanvas.getObjects().length);
        toast.success('Project loaded successfully!');

        // Notify parent that project data has been loaded
        if (onProjectDataLoaded) {
          onProjectDataLoaded();
        }
      } catch (error) {
        console.error('Error loading project data:', error);
        toast.error('Failed to load project data');
      }
    }, 100); // 100ms delay

    return () => clearTimeout(loadTimeout);
  }, [fabricCanvas, loadedProjectData, onProjectDataLoaded]);

  const getCanvasImage = useCallback((format: 'png' | 'jpeg' = 'png'): string | null => {
    if (!fabricCanvas) return null;
    try {
      const dataURL = fabricCanvas.toDataURL({
        format,
        quality: 1,
        multiplier: 2,
      });
      if (dataURL === 'data:,') {
        return null;
      }
      return dataURL;
    } catch (error) {
      console.error('getCanvasImage error:', error);
      return null;
    }
  }, [fabricCanvas]);

  // Expose canvas image getter to parent if requested
  useEffect(() => {
    if (onGetCanvasImage) {
      onGetCanvasImage(getCanvasImage);
    }
  }, [onGetCanvasImage, getCanvasImage]);

  // Backward-compatible export that also triggers download
  const exportCanvas = (format: 'png' | 'jpeg' = 'png') => {
    const dataURL = getCanvasImage(format);
    if (!dataURL) {
      toast.error("Cannot export image due to CORS restrictions. Try using a different image source.");
      return;
    }

    try {
      const link = document.createElement('a');
      link.download = `text-overlay-${Date.now()}.${format}`;
      link.href = dataURL;
      link.click();
      toast.success("Image exported successfully!");
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export image. This might be due to CORS restrictions with the image source.");
    }
  };

  // Handle format changes
  const handleFormatChange = (newFormat: FormatPreset) => {
    if (fabricCanvas) {
      // Update canvas dimensions
      fabricCanvas.setWidth(newFormat.width);
      fabricCanvas.setHeight(newFormat.height);
      fabricCanvas.renderAll();

      setCurrentFormat(newFormat);

      // Update overlay format
      overlay.setSelectedFormat(newFormat);

      // Notify parent component if callback provided
      if (onFormatChange) {
        onFormatChange(newFormat);
      }

      toast.success(`Canvas resized to ${newFormat.name} (${newFormat.width}×${newFormat.height})`);
    }
  };

  const clearCanvas = () => {
    if (!fabricCanvas) return;

    // Only remove text objects, keep the background image
    const objects = fabricCanvas.getObjects();
    objects.forEach((obj) => {
      if (obj instanceof FabricIText || obj instanceof FabricText) {
        fabricCanvas.remove(obj);
      }
    });

    fabricCanvas.renderAll();
    toast.success("Text elements cleared!");
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Canvas</h3>
          <FormatSelector
            currentFormat={currentFormat}
            onFormatChange={handleFormatChange}
            overlayVisible={overlay.isVisible}
            overlayMode={overlay.mode}
            onOverlayToggle={overlay.toggleOverlay}
            onOverlayModeChange={overlay.setOverlayMode}
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={overlay.toggleOverlay}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              overlay.isVisible
                ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
            }`}
            title="Toggle format overlay (Ctrl+O)"
          >
            {overlay.isVisible ? 'Hide Overlay' : 'Show Overlay'}
          </button>
          <button
            onClick={clearCanvas}
            className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 transition-colors"
          >
            Clear Text
          </button>
          <button
            onClick={() => exportCanvas('png')}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
          >
            Export PNG
          </button>
          <button
            onClick={() => exportCanvas('jpeg')}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
          >
            Export JPG
          </button>
        </div>
      </div>

      <div className="border-2 border-canvas-border rounded-lg overflow-hidden bg-canvas-bg shadow-panel relative">
        <canvas
          ref={canvasRef}
          className="max-w-full block"
        />
        {/* Canvas Overlay */}
        {overlay.selectedFormat && (
          <CanvasOverlay
            isVisible={overlay.isVisible}
            selectedFormat={overlay.selectedFormat}
            canvasWidth={currentFormat.width}
            canvasHeight={currentFormat.height}
            overlayMode={overlay.mode}
            className="absolute inset-0"
          />
        )}
      </div>

      {!imageUrl && (
        <div className="text-center text-muted-foreground py-8">
          Upload an image to start adding text overlays
        </div>
      )}
    </div>
  );
};
