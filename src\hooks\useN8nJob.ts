// useN8nJob.ts
import { createSupabaseForJob } from '@/integrations/supabase/client';
import { useEffect, useRef, useState } from 'react';






type JobRow = {
  id: string;
  status: 'queued'|'running'|'done'|'error';
  progress: number;
  result: any | null;
  error: string | null;
  message: string | null;
};

export function useN8nJob() {
  const [job, setJob] = useState<JobRow | null>(null);
  const supabaseRef = useRef<ReturnType<typeof createSupabaseForJob>>();

  // 1) Kick off the webhook
  const startJob = async (formData: FormData) => {
    const res = await fetch('https://genenrativepangea.app.n8n.cloud/webhook/c03bddeb-041f-44f9-b5b8-a17817c50ec5', {
      method: 'POST',
      body: formData,
    });
    if (!res.ok) throw new Error(`Start failed: ${res.status}`);
    const { jobId } = await res.json(); // <-- n8n Respond node returns this

    // 2) Create a scoped supabase client with the header for this job
    const supabase = createSupabaseForJob(jobId);
    supabaseRef.current = supabase;

    // 3) Load initial row
    const { data, error } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', jobId)
      .maybeSingle();
    if (error) throw error;
    if (data) setJob(data as JobRow);

    // 4) Subscribe to row changes (Realtime)
    const channel = supabase
      .channel(`job-${jobId}`)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'jobs', filter: `id=eq.${jobId}` },
        payload => {
          // payload.new contains the latest row
          const next = (payload as any).new as JobRow;
          if (next) setJob(next);
        }
      )
      .subscribe();

    // Return the jobId to the caller in case they need it
    return { jobId, unsubscribe: () => supabase.removeChannel(channel) };
  };

  // Cleanup on unmount
  useEffect(() => () => {
    if (supabaseRef.current) supabaseRef.current.removeAllChannels();
  }, []);

  return { job, startJob };
}
