import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { X, Type, Palette, Layout, Settings } from 'lucide-react';

interface PropertiesPanelProps {
  projectId: string;
  selectedElement: string | null;
  onClose: () => void;
}

const PropertiesPanel = ({ projectId, selectedElement, onClose }: PropertiesPanelProps) => {
  const [activeTab, setActiveTab] = useState('style');
  const [fontSize, setFontSize] = useState([16]);
  const [opacity, setOpacity] = useState([100]);

  return (
    <Card className="h-full bg-card border-border rounded-lg shadow-sm">
      <div className="p-4 h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-card-foreground">Properties</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="style" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Style
            </TabsTrigger>
            <TabsTrigger value="text" className="text-xs">
              <Type className="h-3 w-3 mr-1" />
              Text
            </TabsTrigger>
            <TabsTrigger value="position" className="text-xs">
              <Layout className="h-3 w-3 mr-1" />
              Position
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 mt-4 overflow-y-auto space-y-4">
            <TabsContent value="style" className="space-y-4 mt-0">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Fill Color</Label>
                <div className="grid grid-cols-4 gap-2">
                  {[
                    'hsl(var(--primary))',
                    'hsl(var(--secondary))',
                    'hsl(var(--accent))',
                    'hsl(var(--muted))',
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'
                  ].map((color, i) => (
                    <div
                      key={i}
                      className="aspect-square rounded cursor-pointer border border-border hover:scale-105 transition-transform"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Opacity</Label>
                <Slider
                  value={opacity}
                  onValueChange={setOpacity}
                  max={100}
                  step={1}
                  className="w-full"
                />
                <div className="text-right text-sm text-muted-foreground">
                  {opacity[0]}%
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Border</Label>
                <Input placeholder="Border width (px)" type="number" />
                <div className="grid grid-cols-4 gap-2">
                  {['#000000', '#666666', '#999999', '#CCCCCC'].map((color, i) => (
                    <div
                      key={i}
                      className="aspect-square rounded cursor-pointer border border-border hover:scale-105 transition-transform"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="text" className="space-y-4 mt-0">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Font Family</Label>
                <select className="w-full p-2 border border-border rounded bg-background">
                  <option>Arial</option>
                  <option>Helvetica</option>
                  <option>Times New Roman</option>
                  <option>Georgia</option>
                  <option>Verdana</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Font Size</Label>
                <Slider
                  value={fontSize}
                  onValueChange={setFontSize}
                  min={8}
                  max={72}
                  step={1}
                  className="w-full"
                />
                <div className="text-right text-sm text-muted-foreground">
                  {fontSize[0]}px
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Text Style</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <strong>B</strong>
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <em>I</em>
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <u>U</u>
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Text Align</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Left
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Center
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Right
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="position" className="space-y-4 mt-0">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">X Position</Label>
                  <Input type="number" placeholder="0" />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Y Position</Label>
                  <Input type="number" placeholder="0" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Width</Label>
                  <Input type="number" placeholder="100" />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Height</Label>
                  <Input type="number" placeholder="100" />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Rotation</Label>
                <Slider
                  defaultValue={[0]}
                  min={0}
                  max={360}
                  step={1}
                  className="w-full"
                />
                <div className="text-right text-sm text-muted-foreground">
                  0°
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Layer Order</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Send to Back
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Bring to Front
                  </Button>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </Card>
  );
};

export default PropertiesPanel;