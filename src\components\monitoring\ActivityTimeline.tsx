import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Activity, MessageCircle, Play, CheckCircle, XCircle, Plus } from "lucide-react";

interface ActivityEvent {
  id: string;
  timestamp: string;
  type: 'thread_created' | 'run_started' | 'run_completed' | 'run_failed' | 'message_sent';
  thread_id: string;
  details: Record<string, any>;
}

interface ActivityTimelineProps {
  activities: ActivityEvent[];
  isLoading: boolean;
}

const eventIcons = {
  thread_created: Plus,
  run_started: Play,
  run_completed: CheckCircle,
  run_failed: XCircle,
  message_sent: MessageCircle,
};

const eventColors = {
  thread_created: 'default',
  run_started: 'secondary',
  run_completed: 'default',
  run_failed: 'destructive',
  message_sent: 'outline',
} as const;

const eventDescriptions = {
  thread_created: 'New conversation thread created',
  run_started: 'Assistant run started',
  run_completed: 'Assistant run completed successfully',
  run_failed: 'Assistant run failed with error',
  message_sent: 'Message sent to thread',
};

export function ActivityTimeline({ activities, isLoading }: ActivityTimelineProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffHours > 0) return `${diffHours}h ${diffMinutes % 60}m ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just now';
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return null;
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
          <CardDescription>Loading recent activities...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Activity Timeline
        </CardTitle>
        <CardDescription>
          Real-time feed of system activities and events
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {activities.map((activity) => {
              const Icon = eventIcons[activity.type];
              return (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                    <Icon className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={eventColors[activity.type]} className="text-xs">
                        {activity.type.replace('_', ' ')}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(activity.timestamp)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-foreground mb-1">
                      {eventDescriptions[activity.type]}
                    </p>
                    
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div>Thread: <span className="font-mono">{activity.thread_id.slice(0, 8)}...</span></div>
                      {activity.details.duration && (
                        <div>Duration: {formatDuration(activity.details.duration)}</div>
                      )}
                      {activity.details.status && (
                        <div>Status: <span className="capitalize">{activity.details.status}</span></div>
                      )}
                      {activity.details.assistant_id && (
                        <div>Assistant: {activity.details.assistant_id}</div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {activities.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent activities</p>
                <p className="text-xs">Activities will appear here as they occur</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}