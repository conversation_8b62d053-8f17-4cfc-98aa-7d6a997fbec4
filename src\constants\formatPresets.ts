export interface FormatPreset {
  id: string;
  name: string;
  platform: string;
  width: number;
  height: number;
  aspectRatio: string;
  description: string;
  icon: string;
}

export const FORMAT_PRESETS: FormatPreset[] = [
  // Instagram
  {
    id: 'instagram-post',
    name: 'Instagram Post',
    platform: 'Instagram',
    width: 1080,
    height: 1080,
    aspectRatio: '1:1',
    description: 'Square format for Instagram feed posts',
    icon: '📷'
  },
  {
    id: 'instagram-story',
    name: 'Instagram Story',
    platform: 'Instagram',
    width: 1080,
    height: 1920,
    aspectRatio: '9:16',
    description: 'Vertical format for Instagram Stories',
    icon: '📱'
  },
  {
    id: 'instagram-reel',
    name: 'Instagram Reel',
    platform: 'Instagram',
    width: 1080,
    height: 1920,
    aspectRatio: '9:16',
    description: 'Vertical format for Instagram Reels',
    icon: '🎬'
  },
  
  // YouTube
  {
    id: 'youtube-shorts',
    name: 'YouTube Shorts',
    platform: 'YouTube',
    width: 1080,
    height: 1920,
    aspectRatio: '9:16',
    description: 'Vertical format for YouTube Shorts',
    icon: '🎥'
  },
  {
    id: 'youtube-thumbnail',
    name: 'YouTube Thumbnail',
    platform: 'YouTube',
    width: 1280,
    height: 720,
    aspectRatio: '16:9',
    description: 'Thumbnail for YouTube videos',
    icon: '🖼️'
  },
  
  // TikTok
  {
    id: 'tiktok-video',
    name: 'TikTok Video',
    platform: 'TikTok',
    width: 1080,
    height: 1920,
    aspectRatio: '9:16',
    description: 'Vertical format for TikTok videos',
    icon: '🎵'
  },
  
  // LinkedIn
  {
    id: 'linkedin-post',
    name: 'LinkedIn Post',
    platform: 'LinkedIn',
    width: 1200,
    height: 627,
    aspectRatio: '1.91:1',
    description: 'Horizontal format for LinkedIn posts',
    icon: '💼'
  },
  {
    id: 'linkedin-article',
    name: 'LinkedIn Article',
    platform: 'LinkedIn',
    width: 1584,
    height: 396,
    aspectRatio: '4:1',
    description: 'Cover image for LinkedIn articles',
    icon: '📄'
  },
  
  // Facebook
  {
    id: 'facebook-post',
    name: 'Facebook Post',
    platform: 'Facebook',
    width: 1200,
    height: 630,
    aspectRatio: '1.91:1',
    description: 'Horizontal format for Facebook posts',
    icon: '👥'
  },
  {
    id: 'facebook-story',
    name: 'Facebook Story',
    platform: 'Facebook',
    width: 1080,
    height: 1920,
    aspectRatio: '9:16',
    description: 'Vertical format for Facebook Stories',
    icon: '📖'
  },
  
  // Twitter/X
  {
    id: 'twitter-post',
    name: 'Twitter/X Post',
    platform: 'Twitter/X',
    width: 1200,
    height: 675,
    aspectRatio: '16:9',
    description: 'Image format for Twitter/X posts',
    icon: '🐦'
  },
  
  // Custom/Original
  {
    id: 'custom-original',
    name: 'Original Canvas',
    platform: 'Custom',
    width: 800,
    height: 600,
    aspectRatio: '4:3',
    description: 'Original canvas size (default)',
    icon: '🎨'
  }
];

export const getFormatPresetById = (id: string): FormatPreset | undefined => {
  return FORMAT_PRESETS.find(preset => preset.id === id);
};

export const getFormatPresetsByPlatform = (platform: string): FormatPreset[] => {
  return FORMAT_PRESETS.filter(preset => preset.platform === platform);
};