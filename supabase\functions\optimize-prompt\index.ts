import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const { prompt } = await req.json();
    
    if (!prompt?.trim()) {
      throw new Error('Please provide a prompt to optimize');
    }

    console.log('Optimizing prompt:', prompt);

    const systemPrompt = `You are an expert AI prompt engineer specialized in optimizing text prompts for image generation models like DALL-E, Midjourney, and Stable Diffusion.

Your task is to transform a user's basic prompt into a detailed, high-quality prompt that will generate better images.

Guidelines for optimization:
1. Add specific visual details (lighting, composition, style, colors)
2. Include technical photography terms when appropriate
3. Specify artistic styles if relevant
4. Add quality enhancers (e.g., "high resolution", "detailed", "professional")
5. Maintain the original intent and core concept
6. Keep the prompt under 1000 characters
7. Make it natural and readable

Please optimize this prompt for better image generation results. Respond with only the optimized prompt, no explanations or additional text.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Original prompt: "${prompt.trim()}"` }
        ],
        max_tokens: 500,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      
      if (response.status === 401) {
        throw new Error('Invalid OpenAI API key. Please check your API key and try again.');
      } else if (response.status === 429) {
        throw new Error('OpenAI API quota exceeded. Please check your billing.');
      } else {
        throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
      }
    }

    const data = await response.json();
    const optimizedPrompt = data.choices[0]?.message?.content?.trim();

    if (!optimizedPrompt) {
      throw new Error('Failed to generate optimized prompt');
    }

    // Generate improvements list
    const improvements = generateImprovementsList(prompt.trim(), optimizedPrompt);

    console.log('Prompt optimization completed successfully');

    return new Response(JSON.stringify({
      optimizedPrompt,
      originalPrompt: prompt.trim(),
      improvements
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in optimize-prompt function:', error);
    
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to optimize prompt'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

function generateImprovementsList(original: string, optimized: string): string[] {
  const improvements: string[] = [];
  
  // Simple heuristics to detect improvements
  if (optimized.length > original.length * 1.5) {
    improvements.push("Added detailed visual descriptions");
  }
  
  if (optimized.toLowerCase().includes("lighting") || optimized.toLowerCase().includes("light")) {
    improvements.push("Enhanced lighting specifications");
  }
  
  if (optimized.toLowerCase().includes("style") || optimized.toLowerCase().includes("art")) {
    improvements.push("Improved artistic style guidance");
  }
  
  if (optimized.toLowerCase().includes("high") || optimized.toLowerCase().includes("quality") || optimized.toLowerCase().includes("detailed")) {
    improvements.push("Added quality enhancers");
  }
  
  if (optimized.toLowerCase().includes("color") || optimized.toLowerCase().includes("vibrant") || optimized.toLowerCase().includes("contrast")) {
    improvements.push("Enhanced color specifications");
  }
  
  if (improvements.length === 0) {
    improvements.push("Refined overall prompt structure");
  }
  
  return improvements;
}