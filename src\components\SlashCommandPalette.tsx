import React from 'react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent } from '@/components/ui/popover';
import { SlashCommand } from '@/hooks/useSlashCommands';
import { Camera, Palette, Sun, Settings } from 'lucide-react';

interface SlashCommandPaletteProps {
  isOpen: boolean;
  searchQuery: string;
  filteredCommands: SlashCommand[];
  position: { top: number; left: number };
  onSelect: (command: SlashCommand) => void;
  onSearchChange: (value: string) => void;
  onClose: () => void;
}

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Photography':
      return <Camera className="h-4 w-4" />;
    case 'Art':
      return <Palette className="h-4 w-4" />;
    case 'Lighting':
      return <Sun className="h-4 w-4" />;
    case 'Technical':
      return <Settings className="h-4 w-4" />;
    default:
      return null;
  }
};

export function SlashCommandPalette({
  isOpen,
  searchQuery,
  filteredCommands,
  position,
  onSelect,
  onSearchChange,
  onClose
}: SlashCommandPaletteProps) {
  const groupedCommands = React.useMemo(() => {
    const groups: Record<string, SlashCommand[]> = {};
    filteredCommands.forEach(command => {
      if (!groups[command.category]) {
        groups[command.category] = [];
      }
      groups[command.category].push(command);
    });
    return groups;
  }, [filteredCommands]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed z-50 w-80"
      style={{
        top: position.top,
        left: position.left,
        maxHeight: '300px'
      }}
    >
      <div className="bg-popover border rounded-md shadow-lg">
        <Command>
          <CommandInput
            placeholder="Search styles..."
            value={searchQuery}
            onValueChange={onSearchChange}
            className="border-0"
            autoFocus
          />
          <CommandList className="max-h-48 overflow-y-auto">
            <CommandEmpty>No styles found.</CommandEmpty>
            
            {Object.entries(groupedCommands).map(([category, commands]) => (
              <CommandGroup key={category} heading={category}>
                {commands.map((command) => (
                  <CommandItem
                    key={command.id}
                    value={command.id}
                    onSelect={() => onSelect(command)}
                    className="flex items-start gap-2 p-2 cursor-pointer"
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      {getCategoryIcon(command.category)}
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-foreground">
                          {command.label}
                        </div>
                        <div className="text-xs text-muted-foreground line-clamp-1">
                          {command.description}
                        </div>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </div>
    </div>
  );
}