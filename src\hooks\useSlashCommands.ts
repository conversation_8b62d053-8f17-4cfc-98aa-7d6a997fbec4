import { useState, useEffect, useCallback } from 'react';

export interface SlashCommand {
  id: string;
  label: string;
  description: string;
  category: string;
  value: string;
}

export const SLASH_COMMANDS: SlashCommand[] = [
  // Photography Styles
  { id: 'photo-realistic', label: '/photo-realistic', description: 'Ultra-realistic photographic style', category: 'Photography', value: 'photo-realistic, highly detailed, professional photography' },
  { id: 'cinematic', label: '/cinematic', description: 'Movie-like dramatic lighting and composition', category: 'Photography', value: 'cinematic lighting, dramatic composition, film-like quality' },
  { id: 'portrait', label: '/portrait', description: 'Professional portrait photography style', category: 'Photography', value: 'professional portrait, sharp focus, studio lighting' },
  { id: 'macro', label: '/macro', description: 'Close-up detailed photography', category: 'Photography', value: 'macro photography, extreme close-up, fine details' },
  { id: 'street', label: '/street', description: 'Candid street photography style', category: 'Photography', value: 'street photography, candid, urban environment' },
  { id: 'vintage', label: '/vintage', description: 'Retro film photography aesthetic', category: 'Photography', value: 'vintage film, retro aesthetic, analog photography' },

  // Art Styles
  { id: 'watercolor', label: '/watercolor', description: 'Soft watercolor painting technique', category: 'Art', value: 'watercolor painting, soft brushstrokes, artistic medium' },
  { id: 'oil-painting', label: '/oil-painting', description: 'Rich oil painting texture', category: 'Art', value: 'oil painting, rich textures, traditional art' },
  { id: 'digital-art', label: '/digital-art', description: 'Modern digital illustration', category: 'Art', value: 'digital art, modern illustration, digital painting' },
  { id: 'minimalist', label: '/minimalist', description: 'Clean, simple design approach', category: 'Art', value: 'minimalist design, clean composition, simple elegance' },
  { id: 'abstract', label: '/abstract', description: 'Non-representational artistic style', category: 'Art', value: 'abstract art, non-representational, artistic interpretation' },
  { id: 'pop-art', label: '/pop-art', description: 'Bold, colorful pop art style', category: 'Art', value: 'pop art style, bold colors, graphic design' },

  // Lighting & Mood
  { id: 'golden-hour', label: '/golden-hour', description: 'Warm, soft natural lighting', category: 'Lighting', value: 'golden hour lighting, warm tones, soft natural light' },
  { id: 'dramatic-lighting', label: '/dramatic-lighting', description: 'High contrast, moody lighting', category: 'Lighting', value: 'dramatic lighting, high contrast, moody atmosphere' },
  { id: 'soft-lighting', label: '/soft-lighting', description: 'Gentle, diffused illumination', category: 'Lighting', value: 'soft lighting, gentle illumination, diffused light' },
  { id: 'neon', label: '/neon', description: 'Vibrant neon lighting effects', category: 'Lighting', value: 'neon lighting, vibrant colors, electric atmosphere' },
  { id: 'studio-lighting', label: '/studio-lighting', description: 'Professional studio setup', category: 'Lighting', value: 'professional studio lighting, controlled environment' },

  // Technical Modifiers
  { id: '4k', label: '/4k', description: 'High resolution, ultra-detailed', category: 'Technical', value: '4K resolution, ultra-detailed, high quality' },
  { id: 'wide-angle', label: '/wide-angle', description: 'Expansive field of view', category: 'Technical', value: 'wide-angle lens, expansive view, broad perspective' },
  { id: 'depth-of-field', label: '/depth-of-field', description: 'Focused subject with blurred background', category: 'Technical', value: 'shallow depth of field, bokeh effect, focused subject' },
  { id: 'symmetrical', label: '/symmetrical', description: 'Balanced, symmetrical composition', category: 'Technical', value: 'symmetrical composition, balanced design, mirror effect' },
  { id: 'rule-of-thirds', label: '/rule-of-thirds', description: 'Classic composition technique', category: 'Technical', value: 'rule of thirds composition, balanced framing' },
];

export function useSlashCommands() {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCommands, setFilteredCommands] = useState<SlashCommand[]>([]);

  const filterCommands = useCallback((query: string) => {
    if (!query) {
      setFilteredCommands(SLASH_COMMANDS);
      return;
    }

    const filtered = SLASH_COMMANDS.filter(command =>
      command.label.toLowerCase().includes(query.toLowerCase()) ||
      command.description.toLowerCase().includes(query.toLowerCase()) ||
      command.category.toLowerCase().includes(query.toLowerCase())
    );

    setFilteredCommands(filtered);
  }, []);

  useEffect(() => {
    filterCommands(searchQuery);
  }, [searchQuery, filterCommands]);

  const openCommands = useCallback((query: string = '') => {
    setSearchQuery(query);
    setIsOpen(true);
  }, []);

  const closeCommands = useCallback(() => {
    setIsOpen(false);
    setSearchQuery('');
  }, []);

  return {
    isOpen,
    searchQuery,
    filteredCommands,
    openCommands,
    closeCommands,
    setSearchQuery
  };
}