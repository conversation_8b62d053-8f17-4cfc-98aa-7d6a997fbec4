import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get auth token from request
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { 
      project_name,
      description,
      script,
      style_preferences,
      duration,
      aspect_ratio,
      voice_settings,
      background_music,
      video_type 
    } = await req.json();

    if (!project_name) {
      return new Response(
        JSON.stringify({ error: 'project_name is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Creating UCG video project:', project_name);

    // Create a new marketing campaign for the UCG video
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .insert({
        user_id: user.id,
        name: project_name,
        campaign_type: 'ucg_video',
        status: 'active',
        current_agent: 'video_content',
        campaign_data: {
          description,
          video_type: video_type || 'promotional',
          style_preferences,
          duration: duration || '30-60 seconds',
          aspect_ratio: aspect_ratio || '16:9'
        }
      })
      .select()
      .single();

    if (campaignError) {
      console.error('Error creating campaign:', campaignError);
      throw new Error('Failed to create campaign');
    }

    // Create video project
    const { data: videoProject, error: projectError } = await supabase
      .from('video_projects')
      .insert({
        campaign_id: campaign.id,
        title: project_name,
        script: script || '',
        status: script ? 'script_ready' : 'concept',
        video_concept: {
          description,
          video_type: video_type || 'promotional',
          target_audience: 'general',
          created_at: new Date().toISOString()
        },
        platform_specs: {
          duration: duration || '30-60 seconds',
          aspect_ratio: aspect_ratio || '16:9',
          style_preferences,
          voice_settings: voice_settings || {
            type: 'ai_generated',
            tone: 'professional'
          },
          background_music: background_music || {
            enabled: true,
            type: 'upbeat'
          }
        }
      })
      .select()
      .single();

    if (projectError) {
      console.error('Error creating video project:', projectError);
      throw new Error('Failed to create video project');
    }

    // Log initial conversation
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id: campaign.id,
        agent_type: 'video_content',
        role: 'user',
        message: `Created UCG video project: ${project_name}`,
        context_data: {
          video_project_id: videoProject.id,
          description,
          video_type,
          action: 'project_created'
        }
      });

    // If no script is provided, suggest generating one
    let nextSteps = [];
    if (!script) {
      nextSteps.push({
        action: 'generate_script',
        description: 'Generate a script for your video',
        agent: 'video_content'
      });
    }

    nextSteps.push(
      {
        action: 'create_storyboard',
        description: 'Create a visual storyboard',
        agent: 'video_content'
      },
      {
        action: 'generate_images',
        description: 'Generate images for video scenes',
        agent: 'social_media_image'
      }
    );

    // Automatically trigger script generation if no script provided
    if (!script && description) {
      try {
        console.log('Auto-generating script for UCG video project');
        
        const scriptResponse = await supabase.functions.invoke('video-content-agent', {
          body: {
            campaign_id: campaign.id,
            task: 'create_script',
            platform: 'ucg_video',
            video_length: duration || '30-60 seconds',
            tone: style_preferences?.tone || 'engaging',
            key_message: description
          }
        });

        if (scriptResponse.data?.success) {
          console.log('Script auto-generated successfully');
          nextSteps = nextSteps.filter(step => step.action !== 'generate_script');
          nextSteps.unshift({
            action: 'script_generated',
            description: 'Script has been automatically generated',
            completed: true
          });
        }
      } catch (autoScriptError) {
        console.error('Error auto-generating script:', autoScriptError);
        // Continue without auto-generated script
      }
    }

    console.log('UCG video project created successfully');

    return new Response(JSON.stringify({
      success: true,
      campaign_id: campaign.id,
      video_project_id: videoProject.id,
      project_name,
      status: videoProject.status,
      next_steps: nextSteps,
      project_data: {
        campaign,
        video_project: videoProject
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in create-ucg-video function:', error);
    
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to create UCG video project'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});