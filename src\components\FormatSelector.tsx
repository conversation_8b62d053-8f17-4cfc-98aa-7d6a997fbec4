import { useState } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Badge } from './ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { ScrollArea } from './ui/scroll-area';
import { FORMAT_PRESETS, FormatPreset } from '../constants/formatPresets';
import { Palette, Monitor, Eye, EyeOff, Grid, Crop, Highlighter } from 'lucide-react';
import { OverlayMode } from '../hooks/useCanvasOverlay';

interface FormatSelectorProps {
  currentFormat: FormatPreset;
  onFormatChange: (format: FormatPreset) => void;
  // Overlay controls
  overlayVisible?: boolean;
  overlayMode?: OverlayMode;
  onOverlayToggle?: () => void;
  onOverlayModeChange?: (mode: OverlayMode) => void;
}

export const FormatSelector = ({
  currentFormat,
  onFormatChange,
  overlayVisible = false,
  overlayMode = 'crop',
  onOverlayToggle,
  onOverlayModeChange
}: FormatSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const platforms = [...new Set(FORMAT_PRESETS.map(p => p.platform))];

  const handleFormatSelect = (format: FormatPreset) => {
    onFormatChange(format);
    setIsOpen(false);
  };

  const overlayModeIcons = {
    crop: Crop,
    highlight: Highlighter,
    grid: Grid
  };

  const overlayModeLabels = {
    crop: 'Crop View',
    highlight: 'Highlight',
    grid: 'Grid Lines'
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Monitor className="h-4 w-4" />
          Export: {currentFormat.name}
          <Badge variant="secondary" className="ml-2">
            {currentFormat.aspectRatio}
          </Badge>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Choose Export Format
            </div>

            {/* Overlay Controls */}
            {onOverlayToggle && onOverlayModeChange && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onOverlayToggle}
                  className="gap-2"
                >
                  {overlayVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  {overlayVisible ? 'Hide' : 'Show'} Overlay
                </Button>

                {overlayVisible && (
                  <div className="flex gap-1">
                    {(Object.keys(overlayModeIcons) as OverlayMode[]).map((mode) => {
                      const Icon = overlayModeIcons[mode];
                      return (
                        <Button
                          key={mode}
                          variant={overlayMode === mode ? "default" : "outline"}
                          size="sm"
                          onClick={() => onOverlayModeChange(mode)}
                          className="p-2"
                          title={overlayModeLabels[mode]}
                        >
                          <Icon className="h-4 w-4" />
                        </Button>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue={platforms[0]} className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-6">
            {platforms.map((platform) => (
              <TabsTrigger key={platform} value={platform} className="text-xs">
                {platform}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {platforms.map((platform) => (
            <TabsContent key={platform} value={platform}>
              <ScrollArea className="h-[400px] pr-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {FORMAT_PRESETS
                    .filter(preset => preset.platform === platform)
                    .map((preset) => (
                      <Card 
                        key={preset.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          currentFormat.id === preset.id 
                            ? 'ring-2 ring-primary bg-primary/5' 
                            : ''
                        }`}
                        onClick={() => handleFormatSelect(preset)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <span className="text-2xl">{preset.icon}</span>
                            <Badge variant="outline" className="text-xs">
                              {preset.aspectRatio}
                            </Badge>
                          </div>
                          <CardTitle className="text-sm font-medium">
                            {preset.name}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <CardDescription className="text-xs mb-2">
                            {preset.description}
                          </CardDescription>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{preset.width}×{preset.height}</span>
                            {currentFormat.id === preset.id && (
                              <Badge variant="default" className="text-xs px-2 py-0">
                                Current
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  }
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};