import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get auth token from request
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { 
      campaign_id, 
      platform, 
      asset_type, 
      prompt, 
      style_preferences,
      dimensions,
      brand_guidelines 
    } = await req.json();

    if (!campaign_id || !platform || !asset_type) {
      return new Response(
        JSON.stringify({ error: 'campaign_id, platform, and asset_type are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Social media image agent processing:', { campaign_id, platform, asset_type });

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('*')
      .eq('id', campaign_id)
      .eq('user_id', user.id)
      .single();

    if (campaignError || !campaign) {
      return new Response(
        JSON.stringify({ error: 'Campaign not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Log the conversation
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'social_media_image',
        role: 'user',
        message: `Generate ${asset_type} for ${platform}: ${prompt || 'campaign image'}`,
        context_data: { platform, asset_type, style_preferences, dimensions }
      });

    // Build optimized prompt for image generation
    let optimizedPrompt = prompt || `${asset_type} for ${platform} social media campaign`;
    
    // Add platform-specific optimizations
    const platformSpecs = getPlatformSpecs(platform);
    if (platformSpecs) {
      optimizedPrompt += ` optimized for ${platform} (${platformSpecs.description})`;
    }

    // Add style preferences
    if (style_preferences) {
      optimizedPrompt += `, style: ${style_preferences}`;
    }

    // Add brand guidelines if provided
    if (brand_guidelines) {
      optimizedPrompt += `, following brand guidelines: ${brand_guidelines}`;
    }

    // Add quality enhancers
    optimizedPrompt += ', high quality, professional, engaging, social media ready';

    console.log('Optimized prompt:', optimizedPrompt);

    // Generate image using the generate-ai-image function
    const imageGenerationResponse = await supabase.functions.invoke('generate-ai-image', {
      body: { prompt: optimizedPrompt }
    });

    const imageData = await imageGenerationResponse.json();

    if (imageGenerationResponse.error || !imageData.imageData) {
      throw new Error(`Image generation failed: ${imageData.error || 'Unknown error'}`);
    }

    // Convert base64 to blob and upload to storage
    const imageBuffer = Uint8Array.from(atob(imageData.imageData), c => c.charCodeAt(0));
    const timestamp = Date.now();
    const fileName = `${campaign_id}_${platform}_${asset_type}_${timestamp}.webp`;
    const filePath = `${user.id}/campaigns/${campaign_id}/${fileName}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('public-images')
      .upload(filePath, imageBuffer, {
        contentType: 'image/webp',
        upsert: false
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      throw new Error(`Failed to upload image: ${uploadError.message}`);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('public-images')
      .getPublicUrl(filePath);

    // Create social media asset record
    const { data: asset, error: assetError } = await supabase
      .from('social_media_assets')
      .insert({
        campaign_id,
        asset_type,
        platform,
        asset_url: publicUrl,
        prompt_used: optimizedPrompt,
        agent_created: 'social_media_image',
        status: 'generated',
        asset_data: {
          dimensions: dimensions || platformSpecs?.dimensions,
          style_preferences,
          brand_guidelines,
          file_path: filePath,
          file_name: fileName
        }
      })
      .select()
      .single();

    if (assetError) {
      console.error('Error creating asset record:', assetError);
      throw assetError;
    }

    // Log generation record
    await supabase
      .from('asset_generations')
      .insert({
        campaign_id,
        asset_id: asset.id,
        generation_type: 'social_media_image',
        prompt: optimizedPrompt,
        model_used: 'flux-schnell',
        success: true,
        generation_data: {
          platform,
          asset_type,
          dimensions: dimensions || platformSpecs?.dimensions
        }
      });

    // Log the AI response
    await supabase
      .from('agent_conversations')
      .insert({
        campaign_id,
        agent_type: 'social_media_image',
        role: 'assistant',
        message: `Generated ${asset_type} for ${platform}. Image URL: ${publicUrl}`,
        context_data: { 
          asset_id: asset.id, 
          prompt_used: optimizedPrompt,
          file_path: filePath
        }
      });

    // Update campaign with the current agent
    await supabase
      .from('marketing_campaigns')
      .update({
        current_agent: 'social_media_image',
        updated_at: new Date().toISOString()
      })
      .eq('id', campaign_id);

    console.log('Social media image generated successfully');

    return new Response(JSON.stringify({
      success: true,
      asset_id: asset.id,
      asset_url: publicUrl,
      platform,
      asset_type,
      prompt_used: optimizedPrompt,
      agent_type: 'social_media_image'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in social-media-image-agent function:', error);
    
    return new Response(JSON.stringify({ 
      error: error.message || 'Failed to generate social media image'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

function getPlatformSpecs(platform: string) {
  const specs: Record<string, any> = {
    instagram: {
      description: 'Instagram feed post',
      dimensions: { width: 1080, height: 1080 },
      aspectRatio: '1:1'
    },
    facebook: {
      description: 'Facebook post',
      dimensions: { width: 1200, height: 630 },
      aspectRatio: '1.91:1'
    },
    twitter: {
      description: 'Twitter/X post',
      dimensions: { width: 1200, height: 675 },
      aspectRatio: '16:9'
    },
    linkedin: {
      description: 'LinkedIn post',
      dimensions: { width: 1200, height: 627 },
      aspectRatio: '1.91:1'
    },
    pinterest: {
      description: 'Pinterest pin',
      dimensions: { width: 1000, height: 1500 },
      aspectRatio: '2:3'
    },
    tiktok: {
      description: 'TikTok cover',
      dimensions: { width: 1080, height: 1920 },
      aspectRatio: '9:16'
    }
  };

  return specs[platform.toLowerCase()] || null;
}