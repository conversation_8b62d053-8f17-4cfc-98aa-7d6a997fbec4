import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Eye, EyeOff, Sparkles, Target, TrendingUp } from 'lucide-react';
import loginHero from '@/assets/login-hero.jpg';
const AuthPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const {
    toast
  } = useToast();
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const {
        data,
        error
      } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      if (error) {
        throw error;
      }
      if (data.user) {
        toast({
          title: "Welcome back!",
          description: "Ready to create amazing campaigns?"
        });
        navigate('/');
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Invalid email or password",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  return <div className="min-h-screen flex relative overflow-hidden">
      {/* Animated Background with Floating Shapes */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900">
        {/* Floating geometric shapes */}
        <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-xl animate-float-slow"></div>
        <div className="absolute top-1/4 right-20 w-48 h-48 bg-gradient-to-br from-purple-400/15 to-blue-400/15 rounded-3xl rotate-45 blur-lg animate-float-medium"></div>
        <div className="absolute bottom-1/4 left-1/4 w-40 h-40 bg-gradient-to-br from-cyan-400/20 to-blue-400/20 rounded-full blur-xl animate-float-fast"></div>
        <div className="absolute bottom-10 right-10 w-36 h-36 bg-gradient-to-br from-indigo-400/25 to-purple-400/25 rounded-2xl rotate-12 blur-lg animate-float-slow"></div>
        <div className="absolute top-1/2 left-10 w-24 h-48 bg-gradient-to-br from-blue-400/15 to-cyan-400/15 rounded-full blur-xl animate-float-medium"></div>
        <div className="absolute top-1/3 right-1/3 w-28 h-28 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-lg animate-float-fast"></div>
        
        {/* Additional organic shapes */}
        <div className="absolute top-20 right-1/4 w-56 h-32 bg-gradient-to-r from-blue-400/10 to-transparent rounded-full blur-2xl animate-float-slow"></div>
        <div className="absolute bottom-20 left-1/3 w-44 h-20 bg-gradient-to-l from-cyan-400/15 to-transparent rounded-full blur-xl animate-float-medium"></div>
      </div>
      {/* Left side - Hero Image and Branding */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/10" />
        <img src={loginHero} alt="AI Marketing Dashboard" className="absolute inset-0 w-full h-full object-cover mix-blend-soft-light opacity-80" />
        
        {/* Floating Elements */}
        <div className="absolute top-20 left-20 bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10 shadow-2xl">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-blue-400/20 rounded-xl backdrop-blur-sm">
              <Sparkles className="h-6 w-6 text-blue-300" />
            </div>
            <div>
              <h3 className="font-semibold text-white">AI-Powered</h3>
              <p className="text-sm text-blue-200">Smart Content Generation</p>
            </div>
          </div>
        </div>

        <div className="absolute bottom-32 right-20 bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10 shadow-2xl">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-cyan-400/20 rounded-xl backdrop-blur-sm">
              <Target className="h-6 w-6 text-cyan-300" />
            </div>
            <div>
              <h3 className="font-semibold text-white">Targeted Campaigns</h3>
              <p className="text-sm text-cyan-200">Precision Marketing</p>
            </div>
          </div>
        </div>

        <div className="absolute top-1/2 left-10 bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10 shadow-2xl">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-purple-400/20 rounded-xl backdrop-blur-sm">
              <TrendingUp className="h-6 w-6 text-purple-300" />
            </div>
            <div>
              <h3 className="font-semibold text-white">Growth Analytics</h3>
              <p className="text-sm text-purple-200">Data-Driven Insights</p>
            </div>
          </div>
        </div>

        {/* Main Branding */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center z-10">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent mb-4 p-4">
              Insight Campaign
            </h1>
            <p className="text-xl max-w-md mx-auto leading-relaxed drop-shadow-lg text-emerald-200 font-bold text-center">
              Transform your marketing with AI-powered campaign creation and intelligent content generation
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 relative z-10">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile branding */}
          <div className="lg:hidden text-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent mb-2">
              Insight Campaign
            </h1>
            <p className="text-blue-200">AI-Powered Marketing Platform</p>
          </div>

          <Card className="border-white/10 shadow-2xl bg-white/5 backdrop-blur-xl">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-3xl font-bold text-white">Welcome Back</CardTitle>
              <CardDescription className="text-base text-blue-200">
                Sign in to your account and continue creating amazing campaigns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-white">
                    Email Address
                  </Label>
                  <Input id="email" type="email" placeholder="Enter your email" value={email} onChange={e => setEmail(e.target.value)} required className="h-12 border-white/20 bg-white/10 text-white placeholder:text-white/60 focus:border-blue-400 transition-all duration-200" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-white">
                    Password
                  </Label>
                  <div className="relative">
                    <Input id="password" type={showPassword ? "text" : "password"} placeholder="Enter your password" value={password} onChange={e => setPassword(e.target.value)} required className="h-12 pr-12 border-white/20 bg-white/10 text-white placeholder:text-white/60 focus:border-blue-400 transition-all duration-200" />
                    <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute inset-y-0 right-0 flex items-center pr-4 text-white/60 hover:text-white transition-colors">
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
                <Button type="submit" className="w-full h-12 text-base font-semibold bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white border-0 transition-all duration-200 shadow-lg hover:shadow-xl" disabled={loading}>
                  {loading ? <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      Signing in...
                    </div> : "Sign In"}
                </Button>
              </form>
              
              <div className="mt-6 text-center">
                <p className="text-sm text-blue-200">
                  Don't have an account?{' '}
                  <button className="text-white hover:text-blue-200 font-medium transition-colors">
                    Contact us for access
                  </button>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>;
};
export default AuthPage;