import { useState, useRef } from "react";
import { <PERSON><PERSON><PERSON>, Wand2, <PERSON>I<PERSON>, <PERSON><PERSON>s, Brain, <PERSON>ap, Check, RotateCcw, Edit3, Download, X } from "lucide-react";
import <PERSON><PERSON> from "lottie-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { PromptOptimizationResult } from "@/types/promptOptimizer";
import { SlashCommandPalette } from "./SlashCommandPalette";
import { useSlashCommands, SlashCommand } from "@/hooks/useSlashCommands";

interface AIImageGeneratorProps {
  onImageGenerated: (imageUrl: string, metadata: {
    provider: string;
    prompt: string;
    revisedPrompt?: string;
    model: string;
    size: string;
    quality: string;
    style: string;
    generatedAt: string;
  }) => void;
  onClose: () => void;
}

export const AIImageGenerator = ({ onImageGenerated, onClose }: AIImageGeneratorProps) => {
  const [prompt, setPrompt] = useState("");
  const [optimizedPrompt, setOptimizedPrompt] = useState("");
  const [size, setSize] = useState("1024x1024");
  const [quality, setQuality] = useState("standard");
  const [style, setStyle] = useState("vivid");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationResult, setOptimizationResult] = useState<PromptOptimizationResult | null>(null);

  const [generatedImage, setGeneratedImage] = useState<{
    url: string;
    metadata: any;
  } | null>(null);
  const [cursorPosition, setCursorPosition] = useState({ top: 0, left: 0 });

  const promptRef = useRef<HTMLTextAreaElement>(null);
  const {
    isOpen: isCommandOpen,
    searchQuery,
    filteredCommands,
    openCommands,
    closeCommands,
    setSearchQuery
  } = useSlashCommands();

  // Lottie animation for loading state
  const loadingAnimation = {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 90,
    w: 400,
    h: 400,
    nm: "Loading Animation",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Circle",
        sr: 1,
        ks: {
          o: { a: 0, k: 100 },
          r: { a: 1, k: [{ i: { x: [0.833], y: [0.833] }, o: { x: [0.167], y: [0.167] }, t: 0, s: [0] }, { t: 90, s: [360] }] },
          p: { a: 0, k: [200, 200, 0] },
          a: { a: 0, k: [0, 0, 0] },
          s: { a: 0, k: [100, 100, 100] }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                d: 1,
                ty: "el",
                s: { a: 0, k: [120, 120] },
                p: { a: 0, k: [0, 0] }
              },
              {
                ty: "st",
                c: { a: 0, k: [0.5, 0.3, 0.9, 1] },
                o: { a: 0, k: 100 },
                w: { a: 0, k: 8 },
                lc: 2,
                lj: 2
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0] },
                a: { a: 0, k: [0, 0] },
                s: { a: 0, k: [100, 100] },
                r: { a: 0, k: 0 },
                o: { a: 0, k: 100 }
              }
            ]
          }
        ],
        ip: 0,
        op: 90,
        st: 0,
        bm: 0
      },
      {
        ddd: 0,
        ind: 2,
        ty: 4,
        nm: "Sparkles",
        sr: 1,
        ks: {
          o: { a: 1, k: [{ i: { x: [0.833], y: [0.833] }, o: { x: [0.167], y: [0.167] }, t: 0, s: [0] }, { i: { x: [0.833], y: [0.833] }, o: { x: [0.167], y: [0.167] }, t: 30, s: [100] }, { t: 60, s: [0] }] },
          r: { a: 0, k: 0 },
          p: { a: 0, k: [200, 200, 0] },
          a: { a: 0, k: [0, 0, 0] },
          s: { a: 1, k: [{ i: { x: [0.833], y: [0.833] }, o: { x: [0.167], y: [0.167] }, t: 0, s: [50, 50, 100] }, { t: 45, s: [120, 120, 100] }] }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "sr",
                sy: 1,
                d: 1,
                pt: { a: 0, k: 4 },
                p: { a: 0, k: [0, 0] },
                r: { a: 0, k: 0 },
                ir: { a: 0, k: 8 },
                or: { a: 0, k: 20 }
              },
              {
                ty: "fl",
                c: { a: 0, k: [0.9, 0.6, 0.9, 1] },
                o: { a: 0, k: 100 }
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0] },
                a: { a: 0, k: [0, 0] },
                s: { a: 0, k: [100, 100] },
                r: { a: 0, k: 0 },
                o: { a: 0, k: 100 }
              }
            ]
          }
        ],
        ip: 0,
        op: 90,
        st: 0,
        bm: 0
      }
    ]
  };


  const handleOptimizePrompt = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt to optimize");
      return;
    }

    setIsOptimizing(true);
    try {
      const { data, error } = await supabase.functions.invoke('optimize-prompt', {
        body: { prompt: prompt.trim() }
      });

      if (error) {
        throw new Error(error.message || "Failed to optimize prompt");
      }

      setOptimizationResult(data);
      setOptimizedPrompt(data.optimizedPrompt);
      
      toast.success("Prompt optimized successfully!");
    } catch (error: any) {
      console.error('Error optimizing prompt:', error);
      toast.error(error.message || "Failed to optimize prompt. Please try again.");
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    // Always optimize first, then generate
    setIsOptimizing(true);
    let finalPrompt = optimizedPrompt;
    
    // If no optimized prompt exists yet, optimize first
    if (!optimizedPrompt) {
      try {
        const { data, error } = await supabase.functions.invoke('optimize-prompt', {
          body: { prompt: prompt.trim() }
        });

        if (!error && data) {
          setOptimizationResult(data);
          setOptimizedPrompt(data.optimizedPrompt);
          finalPrompt = data.optimizedPrompt;
          toast.success("Prompt optimized successfully!");
        } else {
          throw new Error(error?.message || "Failed to optimize prompt");
        }
      } catch (error: any) {
        console.error('Error optimizing prompt:', error);
        toast.error("Failed to optimize prompt. Using original prompt.");
        finalPrompt = prompt.trim(); // Fall back to original prompt
      } finally {
        setIsOptimizing(false);
      }
    } else {
      setIsOptimizing(false);
    }

    // Use optimized prompt if available, otherwise use original
    if (!finalPrompt) {
      finalPrompt = prompt.trim();
    }
    
    if (!finalPrompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    if (finalPrompt.length > 1000) {
      toast.error("Prompt must be less than 1000 characters");
      return;
    }

    setIsGenerating(true);

    try {
      // Get aspect ratio from size selection
      const [width, height] = size.split('x').map(Number);
      const aspectRatio = width === height ? "1:1" : width > height ? "16:9" : "9:16";
      
      const { data, error } = await supabase.functions.invoke('generate-ai-image', {
        body: {
          prompt: finalPrompt.trim(),
          size,
          quality,
          style,
          aspectRatio
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        toast.error(`Failed to generate image: ${error.message}`);
        return;
      }

      if (data.error) {
        console.error('AI generation error:', data.error);
        toast.error(`Failed to generate image: ${data.details || data.error}`);
        return;
      }

      // Convert base64 to data URL
      const imageUrl = `data:image/webp;base64,${data.imageData}`;
      
      const metadata = {
        provider: 'replicate-flux',
        prompt: finalPrompt.trim(),
        revisedPrompt: data.revisedPrompt,
        model: 'flux-schnell',
        size,
        quality,
        style,
        generatedAt: new Date().toISOString()
      };

      // Store the generated image for preview
      setGeneratedImage({ url: imageUrl, metadata });
      toast.success("Image generated successfully!");
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error("Failed to generate image. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAcceptImage = () => {
    if (generatedImage) {
      onImageGenerated(generatedImage.url, generatedImage.metadata);
      onClose();
    }
  };

  const handleRegenerate = () => {
    setGeneratedImage(null);
    handleGenerate();
  };

  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;
    
    setPrompt(value);
    
    // Reset optimized prompt when main prompt changes
    if (optimizedPrompt) {
      setOptimizedPrompt("");
      setOptimizationResult(null);
    }
    
    // Check if user typed '/' and show slash commands
    if (value[cursorPos - 1] === '/') {
      const textBeforeCursor = value.substring(0, cursorPos);
      const lastSlashIndex = textBeforeCursor.lastIndexOf('/');
      const searchTerm = textBeforeCursor.substring(lastSlashIndex + 1);
      
      // Calculate cursor position for popup
      if (promptRef.current) {
        const rect = promptRef.current.getBoundingClientRect();
        const lineHeight = 24; // Approximate line height
        const lines = textBeforeCursor.split('\n').length - 1;
        const top = rect.top + (lines * lineHeight) + 30;
        const left = rect.left + 10;
        
        setCursorPosition({ top, left });
        openCommands(searchTerm);
      }
    } else if (isCommandOpen) {
      // Update search query if commands are open
      const textBeforeCursor = value.substring(0, cursorPos);
      const lastSlashIndex = textBeforeCursor.lastIndexOf('/');
      if (lastSlashIndex !== -1) {
        const searchTerm = textBeforeCursor.substring(lastSlashIndex + 1);
        setSearchQuery(searchTerm);
      } else {
        closeCommands();
      }
    }
  };

  const handleCommandSelect = (command: SlashCommand) => {
    if (!promptRef.current) return;
    
    const cursorPos = promptRef.current.selectionStart;
    const textBeforeCursor = prompt.substring(0, cursorPos);
    const textAfterCursor = prompt.substring(cursorPos);
    const lastSlashIndex = textBeforeCursor.lastIndexOf('/');
    
    if (lastSlashIndex !== -1) {
      const beforeSlash = textBeforeCursor.substring(0, lastSlashIndex);
      const newPrompt = beforeSlash + command.value + textAfterCursor;
      setPrompt(newPrompt);
      
      // Set cursor position after the inserted text
      setTimeout(() => {
        if (promptRef.current) {
          const newCursorPos = beforeSlash.length + command.value.length;
          promptRef.current.setSelectionRange(newCursorPos, newCursorPos);
          promptRef.current.focus();
        }
      }, 0);
    }
    
    closeCommands();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (isCommandOpen && (e.key === 'Escape' || e.key === 'Backspace')) {
      if (e.key === 'Escape') {
        closeCommands();
      } else if (e.key === 'Backspace') {
        const cursorPos = e.currentTarget.selectionStart;
        const textBeforeCursor = prompt.substring(0, cursorPos);
        const lastSlashIndex = textBeforeCursor.lastIndexOf('/');
        if (lastSlashIndex === -1 || textBeforeCursor.substring(lastSlashIndex + 1) === '') {
          closeCommands();
        }
      }
    }
  };

  const promptSuggestions = [
    "A modern minimalist workspace with clean lines",
    "Abstract geometric shapes in vibrant colors",
    "A serene landscape with mountains and lakes",
    "Urban cityscape at golden hour",
    "Vintage botanical illustration style",
    "Futuristic technology concept art"
  ];


  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-2 sm:p-4 z-50 overflow-y-auto">
      <div className="w-full h-full flex items-center justify-center">
        <Card className="w-full max-w-7xl my-4 mx-2 sm:mx-4 max-h-[calc(100vh-2rem)] overflow-hidden">
          <CardHeader className="pb-4 border-b">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                AI Image Generator
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
        <CardContent className="h-[calc(100vh-8rem)] overflow-hidden">
          {/* Two Panel Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 h-full">
            {/* Left Panel: Controls (40%) */}
            <div className="lg:col-span-2 space-y-6 overflow-y-auto pr-2">
              {/* Prompt Input */}
              <div className="space-y-2">
                <Label htmlFor="prompt">Describe the image you want to create</Label>
                <Textarea
                  ref={promptRef}
                  id="prompt"
                  placeholder="A professional marketing banner with bold typography and vibrant colors... (Type '/' for style suggestions)"
                  value={prompt}
                  onChange={handlePromptChange}
                  onKeyDown={handleKeyDown}
                  className="min-h-[100px] sm:min-h-[120px] resize-none"
                  maxLength={1000}
                />
                <div className="flex justify-between items-center text-xs text-muted-foreground">
                  <span>{prompt.length}/1000 characters</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    disabled={true}
                    className="h-7 px-3 text-xs"
                  >
                    {isOptimizing ? (
                      <>
                        <Brain className="h-3 w-3 mr-1 animate-pulse" />
                        Optimizing...
                      </>
                    ) : (
                      <>
                        <Zap className="h-3 w-3 mr-1" />
                        AI Optimiter Mode
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* Optimized Prompt Display */}
              {optimizationResult && (
                <div className="space-y-3 p-4 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                      ✨ AI-Optimized Prompt
                    </Label>
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300 bg-white/60 dark:bg-black/20 p-3 rounded border">
                    {optimizedPrompt}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Improvements:</strong> {optimizationResult.improvements.join(", ")}
                  </div>
                </div>
              )}

              {/* Prompt Suggestions */}
              {/* <div className="space-y-2">
                <Label>Quick suggestions:</Label>
                <div className="flex flex-wrap gap-1 sm:gap-2">
                  {promptSuggestions.map((suggestion, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setPrompt(suggestion)}
                      className="text-xs px-2 py-1 h-auto"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div> */}

              {/* Generation Options */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                <div className="space-y-2">
                  <Label htmlFor="size">Size</Label>
                  <Select value={size} onValueChange={setSize}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1024x1024">Square (1024×1024)</SelectItem>
                      <SelectItem value="1024x1792">Portrait (1024×1792)</SelectItem>
                      <SelectItem value="1792x1024">Landscape (1792×1024)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quality">Quality</Label>
                  <Select value={quality} onValueChange={setQuality}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="hd">HD (Higher Cost)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="style">Style</Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="vivid">Vivid (Dramatic)</SelectItem>
                      <SelectItem value="natural">Natural (Realistic)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4">
                <Button 
                  variant="outline" 
                  onClick={onClose} 
                  disabled={isGenerating}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleGenerate} 
                  disabled={isGenerating || !prompt.trim() || isOptimizing || (!prompt.trim() && !optimizedPrompt.trim())}
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  {isGenerating || isOptimizing ? (
                    <>
                      <Wand2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Image
                    </>
                  )}
                </Button>
              </div>

              {/* Info Note */}
              <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
                <p className="font-medium mb-1">💡 Tips for better results:</p>
                <ul className="space-y-1">
                  <li>• Use the "Optimize with AI" button to improve your prompt automatically</li>
                  <li>• Be specific about style, colors, and composition</li>
                  <li>• Include details about lighting and mood</li>
                  <li>• Mention specific art styles if desired (e.g., "watercolor", "minimalist")</li>
                  <li>• Avoid complex scenes with many subjects</li>
                </ul>
              </div>
            </div>

            {/* Right Panel: Preview (60%) */}
            <div className="lg:col-span-3 flex flex-col">
              <div className="flex-1 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 flex flex-col items-center justify-center min-h-[400px] max-h-[600px]">
                {isGenerating ? (
                  // Loading animation
                  <div className="flex flex-col items-center space-y-4 animate-fade-in">
                    <div className="w-32 h-32">
                      <Lottie 
                        animationData={loadingAnimation} 
                        loop={true}
                        className="w-full h-full"
                      />
                    </div>
                    <div className="text-center space-y-2">
                      <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                        Creating your image...
                      </h3>
                      <p className="text-sm text-muted-foreground animate-pulse">
                        This usually takes 10-30 seconds
                      </p>
                    </div>
                  </div>
                ) : generatedImage ? (
                  // Generated image preview
                  <div className="w-full h-full flex flex-col items-center space-y-4">
                    <div className="relative group animate-scale-in flex-1 flex items-center justify-center w-full max-w-full max-h-[400px] overflow-hidden">
                      <img
                        src={generatedImage.url}
                        alt="Generated preview"
                        className="w-full h-full rounded-lg shadow-lg transition-transform group-hover:scale-[1.02] object-contain"
                        style={{ maxWidth: '100%', maxHeight: '100%' }}
                      />
                    </div>
                    
                    {/* Image Actions */}
                    <div className="flex flex-wrap gap-3 justify-center">
                      <Button
                        onClick={handleAcceptImage}
                        className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Accept & Use
                      </Button>
                      
                      <Button
                        onClick={handleRegenerate}
                        variant="outline"
                        disabled={isGenerating}
                        className="border-blue-200 hover:bg-blue-50 dark:border-blue-800 dark:hover:bg-blue-950"
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Regenerate
                      </Button>
                    </div>

                    {/* Image Metadata */}
                    <div className="w-full max-w-md space-y-2 text-sm">
                      <div className="bg-white/60 dark:bg-black/20 p-3 rounded border">
                        <div className="font-medium mb-1">Prompt:</div>
                        <div className="text-muted-foreground text-xs max-h-16 overflow-y-auto">
                          {generatedImage.metadata.prompt}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="font-medium">Size:</span> {generatedImage.metadata.size}
                        </div>
                        <div>
                          <span className="font-medium">Quality:</span> {generatedImage.metadata.quality}
                        </div>
                        <div>
                          <span className="font-medium">Style:</span> {generatedImage.metadata.style}
                        </div>
                        <div>
                          <span className="font-medium">Model:</span> {generatedImage.metadata.model}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Empty state
                  <div className="text-center space-y-4 text-muted-foreground">
                    <ImageIcon className="h-16 w-16 mx-auto opacity-50" />
                    <div>
                      <h3 className="text-lg font-medium mb-2">Preview Area</h3>
                      <p className="text-sm">
                        Enter a prompt and click "Generate Image" to see your AI-generated image here
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <SlashCommandPalette
        isOpen={isCommandOpen}
        searchQuery={searchQuery}
        filteredCommands={filteredCommands}
        position={cursorPosition}
        onSelect={handleCommandSelect}
        onSearchChange={setSearchQuery}
        onClose={closeCommands}
      />
      </div>
    </div>
  );
};